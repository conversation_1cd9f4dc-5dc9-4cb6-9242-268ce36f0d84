学 校： 武汉大学

团队成员：王浩然 贾雁文 饶静雅 陈祖彬 张洪睿

陈宗旭 章 阳 章一诺 许钟元 杨 源

指导老师： 李雨晴 陈晶 王雄

 2025 年 8 月 15 日 hu

第十九届“挑战杯”中国青年科技创新“揭榜挂帅”擂台赛

基于轻量化国产大模型的高帧频弱小目标

检测识别技术研究

幽瞳逐影：国产大模型轻量化实时红外弱

小目标检测识别系统技术方案总结报告

摘要

简要介绍项目背景和核心挑战。

概述本方案采用的核心技术路线。

总结方案的关键创新点。

量化呈现核心成果（例如：模型参数量、在给定测试集上的召回率、虚警率、

时空稳定性等指标）。

在国家安全战略体系中，全天候、全疆域信息优势的获取高度依赖于对高空、远

距离、高机动性弱小目标（如蜂群无人机、隐身飞行器、高超音速导弹）的早期预警

与精确识别能力。 红外搜索与跟踪（ IRST） 系统以其被动探测优势成为关键装备，但

在处理高帧频红外图像序列时面临三重核心挑战：其一，弱小目标在远距离成像时信

号特征极其微弱（信噪比仅 1-5dB），缺乏稳定形状与纹理信息，极易淹没于云层、地

物、海杂波等复杂背景中；其二，传统检测算法依赖固化的先验假设，在动态战场环

境下泛化能力严重不足，而通用视觉大模型虽具强大特征学习能力，却难以适配红外

热辐射特性、弱小目标低像素特性及时序建模需求；其三，机载、弹载、星载等端侧

平台存在严苛的算力、存储与功耗约束（毫秒级响应、瓦特级功耗、兆字节级模型），

与大模型的高计算需求形成尖锐冲突。尤为关键的是，不同类型目标（小目标、大目

标、多目标）对检测策略存在本质性差异化需求，传统的通用检测范式无法兼顾。

为系统性突破上述瓶颈，本项目提出 “场景诊断—特征优化—差异化微调—端侧适

配” 的创新技术路径，其核心突破在于首创目标分类思想驱动的全流程自适应架构：场

景诊断层构建轻量化 MobileNetV2 分类器， 实现毫秒级精准识别小目标、 大目标及多

目标场景，为后续处理提供关键决策。特征优化层创新设计场景驱动动态预处理引擎，

针对小目标采用双域协同降噪强化信号提取，针对大/多目标运用时序背景建模抑制干

扰，显著提升输入数据质量。差异化微调层是核心突破：基于国产 Qwen2. 5-VL 大模

型， 首创 AdaLoRA 动态秩分解微调技术—— 针对小目标/ 多目标设计跳帧长距建模机

制强化微弱目标捕捉与抗遮挡能力；针对大目标构建窗口短距融合机制提升检测稳定

性；融合 YOLOv7 架构并通过双模型特征一致性正则化增强泛化性。端侧适配层提出

跨尺度特征蒸馏框架，结合多层次知识传递与昇腾平台 W8A8 量化压缩，实现模型规

模锐减 60%以上的高效部署。

2

核心技术创新在于： 1) 毫秒级目标分类与动态预处理引擎实现数据源头智能优化；

2) AdaLoRA 场景自适应动态秩微调按目标类型动态调整模型容量；3) 跳帧长距/窗口

短距差异化建模高效提升检测稳定性； 4) 跨尺度特征蒸馏保障轻量化下特征高保真迁

移。

性能指标验证先进性：

● 召回率 > 0.90

● 虚警率 < 0.05

● 时空序列稳定性 > 0.90

● 模型规模压缩率 > 60%

● 单帧处理时延 80ms

关键词： 红外弱小目标检测；目标分类驱动；动态秩微调；自适应架构；昇腾部署

3

目录

1 项目背景...................................................................................................................................... 6

1.1 项目研究背景....................................................................................................................6

1.2 核心技术挑战....................................................................................................................7

1.3 研究目标与意义............................................................................................................... 8

2 国内外研究现状....................................................................................................................... 10

2.1 国防武器装备................................................................................................................. 10

2.1.1 国内武器装备研究现状...................................................................................... 10

2.1.2 国外武器装备研究现状...................................................................................... 12

2.2 红外弱小目标检测......................................................................................................... 13

2.2.1 基于传统方式的红外弱小目标检测................................................................. 13

2.2.2 基于深度学习的红外弱小目标检测................................................................. 15

2.3 模型端侧部署................................................................................................................. 17

2.3.1 模型压缩与优化技术.......................................................................................... 18

2.3.2 端侧推理框架....................................................................................................... 21

3 算法设计方案............................................................................................................................23

3.1 总体技术路线................................................................................................................. 23

3.2 轻量化场景分类器......................................................................................................... 24

3.3 场景自适应数据预处理.................................................................................................26

3.3.1 小目标场景........................................................................................................... 27

4

3.3.2 大目标场景........................................................................................................... 28

3.3.3 多目标场景........................................................................................................... 29

3.4 轻量化国产大模型检测识别........................................................................................ 30

3.4.1 时序数据处理...................................................................................................... 30

3.4.2 检测大模型微调架构设计.................................................................................. 31

3.4.3 模型轻量化与泛化性增强.................................................................................. 33

3.4.4 小目标与多目标：基于跳帧的大模型微调.................................................... 35

3.4.5 大目标微调：基于时间窗口的大模型微调.................................................... 36

4 算法部署方案............................................................................................................................39

4.1 目标部署平台与方案.....................................................................................................39

4.2 模型转化与优化............................................................................................................. 39

4.3 部署环境配置................................................................................................................. 39

4.4 部署验证与性能............................................................................................................. 40

5 验证与实验................................................................................................................................ 42

5.1 实验设置..........................................................................................................................42

5.1.1 实验数据集·.....................................................................................................42

5.1.2 指标设计............................................................................................................. 43

5.1.3 检测与指标计算策略....................................................................................... 45

5.2 实验结果与分析............................................................................................................. 45

6 总结与展望................................................................................................................................ 46

6.1 工作总结..........................................................................................................................46

6.2 未来展望..........................................................................................................................47

5

参考文献....................................................................................................................................... 49

6

7

1.1

1

来，以深度学习，特别是视觉大模型为代表的大数据驱动方法，凭借其从海量数据中

自主学习深层抽象特征和复杂上下文关联的强大能力，为突破这一瓶颈带来了曙光。

理论上，大模型能够更深刻地理解目标的时空演化规律与背景的动态干扰模式，从而

实现更鲁棒的区分。

但通用大模型在国防装备端侧平台的红外弱小目检测中存在显著性适配问题。机

载、弹载、星载等典型作战平台在算力、能耗、存储等方面均受到严苛的物理约束，

要求算法具备毫秒级响应速度、瓦特级功耗与兆字节级模型规模。相比之下，当前主

流视觉大模型往往拥有数十亿级参数量和数百至数千 GFLOPs 的计算需求，与端侧部

署条件存在天然冲突。更为关键的是，红外弱小目标与普通可见光图像目标在成像机

项目背景

理、光谱特性和特征分布上存在显著差异：红外图像缺乏稳定的纹理与色彩信息，目

标信号常被强背景杂波淹没，这使得未经适配的通用视觉模型难以直接捕捉到关键判

项目研究背景

在现代多域联合作战与国家安全战略体系中，能否掌握全天候、全疆域的信息优

势，已成为决定胜负的关键。对高空、远距离、高机动性目标的早期预警与精确识别

能力，是构成这一信息优势的核心支柱。红外搜索与跟踪（Infrared Search and Track，

IRST） 系统， 凭借其被动探测的隐蔽性、 强大的抗电磁干扰能力以及穿透烟尘雾霾的

特性，成为现代侦察预警、精确打击和武器制导系统中不可或缺的“火眼金睛”。

然而，在应对高帧频红外图像序列中的“弱小目标”时，现有技术正面临前所未有

的挑战。这些目标，如蜂群无人机、隐身飞行器、临近空间高超音速导弹等新质作战

力量，在远距离成像时，信号特征极其微弱，通常仅在焦平面上占据数个像素，缺乏

可供辨识的稳定形状、轮廓与纹理信息。其微弱的红外辐射信号极易淹没在复杂的云

层、崎岖的地物、波动的海面乃至城市热岛效应形成的背景杂波之中，信噪比极低，

使得传统的检测技术遭遇瓶颈。

传统基于模型驱动的检测算法，例如空域滤波[ 32][ 33]、形态学处理[ 61][ 62]等，虽计算

开销小、易于实现，但其设计哲学根植于对目标和背景的先验假设（如目标比邻域更

亮、背景连续平滑等）。当面对动态多变、非线性的复杂战场环境时，这些固化的假

设极易失效，导致算法泛化能力严重不足，虚警率和漏检率难以满足实战需求。近年

8

1.2

1.3

1.

2.

3.

4.

。 [5][17]

多目标场景的差异化适配挑战：不同类型目标（小目标、大目标、多目标）对

检测策略需求迥异：小目标需强化局部细节特征以对抗噪声；大目标需利用时

序连续性提升稳定性；多目标需解决遮挡、重叠导致的特征混淆。采用 “一刀

切” 的统一检测流程无法兼顾各类场景，易导致小目标漏检、大目标时序跳变

等问题。

研究目标与意义

别特征。因此，需要通过对模型结构与参数进行有针对性的压缩与适配训练，使其在

为应对上述挑战，本研究的核心目标是构建一套国产大模型轻量化实时红外弱小

目标检测识别系统。 具体研究目标围绕挑战展开：

满足端侧部署约束的同时，仍能保有对红外弱小目标的敏感性与判别力。

核心技术挑战

在资源受限的国产化平台上实现高帧频、高精度的红外弱小目标检测，技术层面

面临着相互交织的四大核心挑战：

目标特征微弱与背景强干扰的双重制约：目标信号经大气吸收、散射后衰减严

1-5dB 重， 抵达传感器时与背景起伏、 探测器噪声处于同一量级（ 信噪比 ），

有效特征严缺失[ 29]。 同时， 云层边缘、 地物轮廓、 海天杂波等干扰源在形态、

亮度上与目标高度相似，传统滤波方法难以区分，导致虚警率居高不下[33]。

通用大模型的任务适配难题：通用视觉大模型无法直接适配于红外域：一是红

外热辐射特征与可见光纹理特征差异显著，预训练视觉编码器难以有效提取目

标信息 [ 52]；二是弱小目标的低像素特性导致模型注意力难以聚焦，易被背景噪

声稀释 [ 59]；三是缺乏针对红外序列时序关联性的建模机制，单帧检测难以利用

运动连续性提升稳定性[60]。

大模型轻量化与性能保持的平衡矛盾：端侧平台的算力（如弹载嵌入式系统）、

存储（通常 <100MB）和功耗（瓦特级）约束，与大模型的高计算需求形成尖

锐冲突。单纯压缩模型参数可能导致特征提取能力退化，而如何在剪枝、量化

等轻量化改造中保留对弱小目标的高精度检测能力，是亟待解决的核心问题

1. 提升微弱目标特征提取与抗背景干扰能力：显著提升系统在极低信噪比（1-5dB）

条件下对红外弱小目标的特征感知能力，有效抑制复杂背景（云层、地物、海

杂波、热岛效应等）干扰，为精准检测奠定基础。

2. 实现国产大模型的高效任务适配：克服通用视觉大模型与红外弱小目标检测任

务的鸿沟，通过对国产大模型进行深度领域适配与优化，使其能够有效理解红

外热辐射特征、聚焦微弱目标信号、并利用时空上下文信息，显著提升模型在

该特定任务上的判别力与鲁棒性。

3. 达成大模型轻量化与端侧实时部署：在严格满足端侧平台（机载、弹载、星载）

的算力、存储和功耗约束条件下，通过先进的模型压缩与硬件协同优化技术，

将适配后的大模型转化为轻量化形态，确保其在国产硬件（如昇腾芯片）上实

现高帧频（如 XX fps）实时检测，同时最大程度保持检测精度。

4. 构建场景自适应的检测系统：使系统能够智能应对小目标、大目标、多目标

（含遮挡）等差异化场景，通过自适应机制优化检测策略，确保在各种复杂战

场环境下均能维持高检测率与低虚警率。

5. 达成国际先进水平的性能指标：在标准红外弱小目标测试集上，系统整体性能

目标为：召回率 > 0.90、虚警率 < 0.05、序列稳定性 > 0.90，并在国产硬件平台

上验证其工程可行性。

本研究的核心价值在于为我国国防装备提供突破“卡脖子”难题的高性能红外弱小

目标检测解决方案。其意义不仅体现在解决具体军事需求上，更在于开创性地探索了

在极端严苛资源约束（算力、存储、功耗）下部署高性能人工智能感知系统的可行路

径。通过在国产化硬件平台上实现轻量化大模型的高效任务适配与实时运行，本研究

将为移动计算、工业物联网、无人系统等广泛领域的边缘智能应用提供关键的技术借

鉴和实践范例，推动资源受限环境下复杂 AI 模型的实用化进程。

更为深远的意义在于全面夯实国防核心技术的自主可控根基。面对复杂严峻的国

际形势，掌握从基础模型、核心算法到硬件平台的全链条自主研发能力，是构筑国家

安全科技屏障的必然要求。本研究立足国产大模型和昇腾等国产 AI 芯片，致力于打通

“理论创新-技术突破-工程实现-装备应用”的完整国产化链条。其成功实施将显著提升

我国主战装备在复杂电磁环境下信息感知的智能化、精准化与自主化水平，有效突破

9

外部技术封锁，为维护国家安全、掌握未来战争主动权提供坚实可靠的技术支撑，具

有重大而紧迫的战略意义。

10

11

2.1.1

2.1

2

（1）导弹预警与精确制导系统

在导弹预警领域， 长春光机所基于 YOLOv4 框架开发的空间红外弱目标检测技术

，通过增加 104×104 特征尺度和 K-means 聚类优化先验框尺寸，显著提升了复杂云 [ 18]

层背景下的目标检出率， 检测速度达 38. 99ms/ 帧， 大幅降低云层干扰下的漏检率。该

技术已应用于我国新一代天基预警系统，实现对高超音速导弹的早期探测。火箭军工

程大学则专注于弹载平台的轻量化实时检测，提出 YOLO-IDSTD 模型[ 21]，通过 Focus

模块减少推理耗时，采用四尺度检测和路径聚合网络强化特征融合，使模型尺寸压缩

至 7.27MB，满足弹载嵌入式系统对实时性和低功耗的严苛要求。西北工业大学的先检

国内外研究现状

测后跟踪技术[ 22] 采用增强型简化高通滤波预处理， 结合自适应阈值分割， 在低信噪比

条件下仍能保持高检测概率，已应用于空空导弹的红外成像制导系统。

近年来，随着传感器性能提升与计算机视觉技术的快速发展，基于高帧频红外图

像序列的弱小目标检测与识别技术在军事侦察、航空航天、灾害监测等领域得到了广

泛研究与应用。红外弱小目标往往是潜在威胁或关键目标的早期表现，其在早期预警

与态势感知中具有不可替代的作用。国内外学术界和工业界围绕目标特征提取、背景

抑制、检测识别算法优化等方面开展了大量研究，形成了从传统图像处理方法到深度

学习、大模型驱动算法的多种技术路线。然而，现有方法在低信噪比下的稳定性、复

杂背景干扰的抑制能力、以及轻量化与高精度的平衡方面仍存在不足。本章将系统地

梳理高帧频红外弱小目标检测与识别的研究现状，分析各类方法的优缺点与发展趋势，

为后续基于轻量化国产大模型的技术方案提供支撑。

国防武器装备

红外弱小目标检测技术作为现代国防武器装备的核心能力之一，直接关系到导弹

预警、反无人机作战、隐身目标识别等关键军事能力的效能，目前已成为各国国防武

器竞争的战略高地。

国内武器装备研究现状

我国在红外弱小目标检测领域的武器化应用研究在导弹预警、反无人机、舰载防

御等领域取得显著突破。表 2-1 总结了部分国内代表性红外弱小目标检测武器系统。

12

（2）反无人机与集群目标识别

针对“低慢小”无人机的防御需求，基于密度-距离空间的红外检测方法[ 20]通过局部

灰度峰值特性构建二维空间模型，实现多目标自适应检测，平均每帧处理时间仅

0.0212 秒，在连续杂波背景下 AUC 值接近 1，且不受目标尺寸形状变化影响，已集成

于野战防空系统的反无人机模块。针对集群目标识别难题，武汉大学提出改进的

IDBSCAN-DM 算法[25]，通过多尺度滑动窗口快速提取候选目标，融合显著性和分布特

征，有效解决了无人机群、多弹头分导等集群目标的特征提取难题，显著提升了复杂

战场环境下对饱和攻击的应对能力，检测概率较传统方法提高 30%以上。

（3）舰载防御与激光反制系统

在舰载防御领域，西安电子科技大学开发的逐像素注意力网络[ 19]采用 U 形注意力

块和稠密融合技术，通过增强小目标的空间与通道特征，有效抑制海杂波干扰，在

NUDT-SIRST 数据集检测率达 98. 84%，虚警率仅 2. 92×10-6，已应用于舰载红外搜索

与跟踪系统。 西安知语云公司集成的激光反制系统[ 23] 则融合量子阱红外探测器、 雷达

与频谱监测模块，可识别-40℃下 0. 1℃温差，探测精度达 98. 7%，实现与空管雷达联动，

成功完成对巡航导弹靶机的拦截验证，该系统代表了国内在多模态传感集成领域的最

高水平，已在多个重点军事设施部署应用。

表 2-1：国内代表性红外弱小目标检测武器系统

天基预警系统

弹载制导模块

野战防空系统

舰载 IRST 系统

反无人机系统

武器

多尺度 YOLOv4 增强

YOLO-IDSTD 轻量化

激光-雷达-红外融合

逐像素注意力网络

密度-距离空间模型

技术

38.99ms/帧，云层漏检率降低

40%

CPU 推理速度提升 36.1%，模

型 7.27MB

探测精度 98.7%，温差分辨率

0.1℃

Pd=98.84%，Fa=2.92×10-6

0.0212s/帧，AUC≈1

指标

高超音速导弹预警

空空/地空导弹导引

头

巡航导弹拦截

舰艇防空反导

“低慢小”无人机拦截

应用

2.1.2 国外武器装备研究现状

全球军事强国正加速推进高帧频红外弱小目标检测技术在武器平台的集成应用，

形成以全景感知、 电子对抗和隐身探测为核心的发展路线。 表 2-2 总结了部分国外代

表性红外弱小目标检测武器系统。

欧美国家在舰载防御系统和电子对抗领域保持技术领先。法国泰雷斯公司升级的

Murin 雷达采用 X 波段电子扫描阵列，支持超 50 个目标同步跟踪，探测距离达 20 公

里(车辆)，8 公里(人员)，新增跳频和低截获概率波形技术，显著增强抗电磁干扰能力，

适用于连级机动部署。美国海军研发的 SPEIR 舰载光电系统[ 23]在 Block 1 阶段已实现

360°被动光学探测与激光测距，Block 2 阶段将整合 AI 驱动的 LockNESS 算法，可自

动生成威胁全景图并引导软杀伤系统，该系统代表了舰载光电防御的最高水平，其多

目标实时跟踪能力对反舰导弹饱和攻击具有显著抑制效果。

在反无人机领域，美国 Skylark Labs 开发的 ARIES 系统[23]采用光学传感器与 AI 融

合方案， 突破传统射频监测局限， 实现对无射频信号“ 暗无人机” 的探测， 在 Camp

Atterbury 演习中成功识别多型未知构型无人机， 填补非辐射目标探测空白。 美国海军

研究实验室开发的神经时空张量模型[ 24] 结合低秩张量分解与三维全变分约束， 在动态

背景中实现 17fps 实时检测，模型参数量减少至传统方法的 1/16.6，适用于边缘计算设

备，已部署于单兵便携式反无人机系统。

印度和俄罗斯在隐身目标探测和抗干扰算法领域取得突破性进展。印度国防研究

与发展组织 DRDO 开发的光子雷达利用光波替代无线电波进行探测，宣称可识别 F-35、

J-20 等隐身战机，计划于 2025 年底开展对抗性气候测试，目标是与 Akash-NG 防空网

络协同，挑战现有隐身技术优势。伊朗锡斯坦大学提出的抗噪声检测算法通过噪声像

素抑制提升检测鲁棒性， 在 431 幅含强噪图像中虚警率显著低于传统方法。 该技术特

别适用于中东地区的沙尘暴环境，已集成于伊朗“信仰”-373 防空系统。

俄罗斯则重点发展红外-雷达复合探测技术，其 S-500 防空系统[ 24]采用双波段红外

探测与有源相控阵雷达协同工作机制，通过时空特征融合张量模型增强弱小目标检测

能力，在乌克兰战场实际部署中展现出对低空巡航导弹的优异拦截能力。

表 2-2：国外代表性红外弱小目标检测武器系统

13

14

2.2.1

2.2

差异设计出能够过滤背景噪声的滤波器，从而实现检测目标。这种检测方法实现简单，

但仅仅通过频率差异进行区分出背景噪声和目标，无法应对复杂背景下的检测问题，

鲁棒性差[43]。基于滤波的方法主要分为以下两种实现方法。

第一种是基于空域滤波算法，主要利用目标与背景在空间分布上的统计差异，通

过分析邻域像素间的空间关联特性来估计背景灰度分布，进而通过差分运算实现目标

的增强显示。Marvasti 等人[32]在 2018 年的研究中，针对传统 Top-Hat 变换在目标识别

方面的不足，提出了改进的 Top-Hat 变换算法，增强了对真实目标的识别能力。Wang

等人[ 33] 通过设计多向结构单元形态变换融合机制， 在充分挖掘目标与背景对比度信息

的基础上，有效降低了复杂背景条件下的虚警率。

第二种是基于频域滤波算法，专注于目标、背景和噪声在频率域的本征差异，通

过在频域空间构建专门的滤波器来实现背景与噪声的有效抑制。为解决高维特征空间

红外弱小目标检测

现阶段，红外弱小目标检测手段主要分为两大类：其一为基于传统方式的红外弱

小目标检测，包括基于滤波的方法、基于人类视觉系统的方法和基于图像数据结构的

方法；其二是基于深度学习的红外弱小目标检测[29]。

基于传统方式的红外弱小目标检测

（1）基于滤波的方法

小目标检测最早采用基于滤波方式，根据分析噪声、背景及目标在频域上的频率

国家

法国

美国

印度

伊朗

俄罗斯

武器

Murin 雷达升级

版

SPEIR 舰载系统

ARIES 系统

光子雷达

BLCM 抗噪算法

红外-雷达复合

探测

技术

X 波段 ESA 扫描+LPI

波形

360°被动光学

+LockNESS AI

光学-AI 融合

光波替代无线电波

分支局部对比度测度

时空特征融合张量模型

指标

超 50 目标同步跟

踪，20km 车辆探测

多目标实时跟踪/分类

无射频信号目标检测

高精度隐身目标识别

强噪环境下低虚警率

低空巡航导弹拦截能

力优异

应用

连级机动部署

2025 年作战测试

Camp Atterbury

验证

2025 年气候测试

集成于“信仰”-373

防空系统

集成于 S-500 防

空系统

的适用性问题，Anju 和 Raj 等人[ 34] 系统性地提出了包括剪切波变换在内的多尺度变换

方法族。Wang 等人[35]提出了基于非负约束变分模态分解的检测框架，该方法的显著优

势在于无需预设参数配置，即使面对窄频带信号也能实现有效的成分分离。

（2）基于人类视觉的方法

基于人类视觉的方法的核心在于对人类视觉系统显著性感知过程的计算建模，通

过量化目标在局部邻域内的显著性特征来实现检测功能。这类方法的理论基础建立在

目标与背景在空间、灰度、纹理等多个维度上的不连续性假设之上，通过构建局部对

比度或显著性映射的计算框架来突出目标区域[26]。

Chen 等人[ 36]提出局部对比度检测理论，通过量化目标在空间分布上不连续特性[ 37]，

建立基于 3×3 邻域灰度差异的像素显著性评估机制。 尽管该方法在计算效率上表现优

异， 但在复杂背景环境下抗噪声能力仍需改进。 Han 等人[ 38] 通过重新设计邻域计算策

略，显著减少冗余的像素比较操作，在保持检测精度的前提下大幅提升算法执行效率。

针对不同类型目标的特性差异， Wei 等人[ 39] 提出了多尺度块图像对比度分析方法。

采用分块处理策略，通过在各个图像块内独立计算对比度，最终通过多尺度融合机制

生成综合显著性映射，从而在复杂背景条件下展现出更强的目标检测能力。Bai 等人[ 40]

提出了基于导数特征的对比度测试框架，通过计算图像的多阶导数信息，在各个导数

子带上构建对比度映射，最终通过加权融合策略生成最终的显著性图像，有效捕获了

目标的边缘和纹理特征。

针对高亮度背景抑制问题， Han 等人[ 41] 设计了基于匹配滤波器和最近均值原理的

背景估计模型，通过精确估计局部背景均值并利用匹配滤波器增强目标信号，实现了

目标与背景的有效分离。Lu 等人[ 42]提出的多方向导数加权对比度测量方法，通过深度

挖掘图像导数特性并结合创新的局部对比度测量机制，进一步提升检测算法的鲁棒性。

（3）基于图像数据结构的方法

基于图像数据结构的方法是红外弱小目标检测中的另一类重要方法，通过深入分

析图像数据的内在结构特性——包括稀疏性、低秩性、张量结构等，实现目标与背景

的有效分离。这类方法优势在于能够充分利用目标和背景在数据结构层面的本质差异，

通过构建相应的数学优化模型并设计高效的求解算法来实现精确的目标检测[29]。

Zhao 等人[ 44]提出一种基于稀疏表示的目标检测方法，通过构建过完备字典将图像

分解为稀疏目标分量和低秩背景分量，有效提升目标与背景的分离效果。在此基础上，

Zhao 等人[ 45]提出一种结合稀疏性和边缘保留平滑的结构协同稀疏性检测算法，结合了

15

稀疏性和边缘保留平滑技术。通过在梯度零范数基础上提取大梯度分量来改善背景建

模，同时利用行范数对背景稀疏性进行建模，并通过误差矩阵的列范数实现红外弱小

目标的精确定位，从而显著增强了算法的鲁棒性表现。

针对复杂背景下的目标检测问题，Liu 等人[46]提出一种通过联合低秩和局部平滑度

先验进行红外弱小目标检测方法，通过滑动三维窗口构建改进的时空模型，利用张量

相关总变分表征背景，并采用范数约束消除强残差，最后设计高效的交替方向乘子法

求解模型。Yin 等人[ 47]提出了一种新的 3-D 范式框架，将时空加权和正则化结合在低

秩稀疏张量分解模型中，通过设计时空局部先验结构张量、引入三向对数的张量核范

数和采用加权三向总变分正则化来区分目标与背景并约束平滑度，同时开发基于乘子

交替方向方法的高效求解方法及快速张量方程加速子问题求解。

针对动态背景适应、 转置误差及稀疏性近似等关键问题， Sun 等人[ 48] 提出自适应

时空红外张量和加权张量平均秩近似的解决方案，通过交替方向乘子法求解，有效解

决低秩稀疏分解方法在动态背景适应方面的固定时间步长限制、低秩张量恢复过程中

的转置误差敏感性以及稀疏度次优 L1 范数近似导致的物理表示不准确等关键技术难题。

在稀疏性与低秩性融合研究方向， Wei 等人[ 49] 提出基于时间低秩和稀疏表示的鲁

棒检测方法，通过充分利用目标补丁的时间性和低秩性质，将目标检测问题转化为低

秩稀疏学习问题，并通过时间字符组合策略缩小检测区域，减少背景干扰并提升检测

的准确性和效率。Deng 等人[ 50]提出局部和非局部先验融合检测方法，通过引入基于滑

动双窗口的创新局部对比度测量机制，并与低秩稀疏分解技术相结合，增强目标和背

景之间的分离能力， 特别是在抑制背景中 PNHB 和高强度结构方面表现突出。 Huang

等人[ 51] 提出两阶段特征互补改进张量低秩稀疏分解方法， 利用本地先验信息初始化

TLRSD 模型，有效增强本地和非本地特征之间互补性，实现更加精确的目标定位效果。

2.2.2 基于深度学习的红外弱小目标检测

传统的红外弱小目标检测方法很大程度上依赖于人工设计的特征提取和先验知识，

通过固定的滤波和建模流程来获取目标特征，这种固化的处理模式限制了其在不同应

用场景下的适应能力和优化空间。相比之下，深度学习运用端对端的学习模式，神经

网络能够自主从数据当中挖掘目标的深层次特征，从而实现检测性能的显著提升。

生成对抗网络在红外弱小目标检测领域的应用展现出独特的优势。Zhao 等人[ 52]提

出基于生成对抗网络的检测框架，通过生成器对复杂背景的精确建模和判别器对目标

16

区域的专门识别，显著降低误警率。Ma 等人[53]在此基础上进一步发展了生成对抗差分

分析模型，通过快速行进法构建伪背景标签，利用背景引导的生成对抗网络学习背景

数据的潜在分布特性，再通过差分分析获得包含小目标感兴趣区域的差分图像，最终

对感兴趣区域进行精细化表征以获得检测结果。这种方法的优势在于充分考虑了图像

背景对目标检测的影响，从而提高检测的准确率。Ding 等人[ 54]针对训练数据不足这一

制约卷积神经网络性能的关键问题，提出两阶段红外小目标图像增广策略，通过背景

填充和扩散模型生成高质量且多样化的复杂背景图像，并利用基于生成对抗网络的目

标自适应融合技术实现目标与背景的无缝整合，生成高度逼真的红外小目标增强图像。

针对红外弱小目标尺寸微小、特征表达不明显问题，Qi 等人[55]提出 Transformer 和

CNN 融合的检测架构，该架构采用双分支设计：CNN 分支通过具有跳跃连接的 U-Net

结构获取小目标的低级局部特征，而 Transformer 分支则利用分层自注意机制学习长程

上下文依赖关系，从而抑制背景干扰并增强目标特征表达。此外，设计特征融合模块

实现了两个分支特征的有效整合，进而实现更加精确的目标检测。

YOLO 系列模型在红外弱小目标检测领域展现出卓越的性能表现。Shi 等人[56]提出

改进 YOLOv3 算法，通过将特征提取阶段的 32 倍下采样检测头替换为 4 倍下采样检测

头来适应小目标的稀疏分布特性，并在特征融合阶段引入红外 IAM 聚焦机制实现感兴

趣区域的特征聚焦，从而显著提升了检测性能。Liu 等人[57]设计倒置残差多尺度膨胀网

络，通过倒置残差特征提取模块实现红外小目标特征的有效提取，借助频率加权特征

融合技术平衡目标定位与噪声抑制之间的关系，并利用倒置残差多尺度扩张注意力模

块降低背景噪声的影响，增强了对弱小目标的感知能力。

考虑到实际应用中对实时性的严格要求，Gupta 等人[58]提出轻量级目标检测网络架

构，通过模型压缩和计算加速策略，在维持检测精度的前提下显著降低了计算复杂度。

Ma 等人[59]为了减少模型参数规模，提出多尺度目标上下文特征提取模块，能够丰富目

标的特征表达能力；并设计融合深浅层特征的特征映射上采样网络架构，提升轻量化

红外弱小目标检测网络的特征映射能力。

在数据增强和模型训练优化方面，Wu 等人[60]提出结合空间低频 StyleGAN 背景生

成网络和检测难度驱动的 Transformer 目标融合模型的两阶段数据增强策略，通过改进

红外背景生成质量和自适应目标强度融合技术，有效缓解红外小目标检测中训练数据

不足的问题。

17

图 2-1 展示了红外弱小目标检测研究方法时间轴。 综合分析上述各类检测技术的

研究现状可以发现，传统的基于滤波、人类视觉和图像数据结构的方法主要通过深入

分析图像中目标和背景灰度特性，实现背景抑制和目标-背景对比度增强的目标。尽管

这些方法在特定场景下表现良好，但其较高的计算复杂度和有限的场景适应能力制约

了实际应用效果。相比之下，基于深度学习的检测技术通过引入先进的特征提取网络

架构，能够挖掘目标更深层次的特征信息，相较于传统方法在复杂应用场景下展现出

更强的适应能力和鲁棒性表现。然而，现有深度学习算法在检测精度和误警率控制方

面仍存在进一步优化的空间。

图 2-1 红外弱小目标检测研究方法时间轴

2.3 模型端侧部署

在高帧频红外弱小目标检测的实战环境中，机载、弹载、星载等平台往往需在算

力、功耗与存储受限条件下，实现毫秒级响应和高精度识别。然而，依赖云端计算不

仅会引入通信延迟，还会在战时面临链路受限、干扰甚至中断的风险；同时，敏感红

18

19

（2）剪枝技术

剪枝旨在移除冗余参数以降低模型复杂性，从而提升推理速度与部署效率。Hu 等

人 提出神经元剪枝方法，基于数据驱动分析，裁剪网络中贡献较小的神经元，减少计 [3]

算量。Molchanov 等人 设计卷积核剪枝策略，通过评估卷积核的重要性移除低贡献卷 [ 4]

积核，显著降低计算开销。He 等人 提出通道剪枝方法，在保证精度前提下减少通道 [ 5]

数量以适配端侧硬件，这类结构化剪枝方法与并行计算架构高度兼容，但压缩率相对

有限。相比之下，Guo 等人 提出动态剪枝方法，在推理过程中根据输入特征动态移除 [6]

Liu Liu 权重， 等人 利用频域信息进行动态剪枝以提升压缩率， 等人 提出自动结构化 [7] [8]

剪枝框架，通过搜索最优剪枝策略实现超高压缩率。尽管非结构化剪枝能够获得更高

外成像数据外传存在安全隐患，与国防装备的高安全性要求相悖。因此，将检测模型

的参数压缩比，但其在硬件加速上的适配性较弱。表 2-3 总结了常见的模型剪枝方法。

表 2-3：常见模型剪枝方法对比

直接部署在任务平台本地，成为兼顾实时性与安全性的必然选择。为了实现端侧部署，

研究者们主要从两大方向展开优化：一是通过模型压缩与优化技术减少参数规模与计

算量，从源头降低硬件负担；二是借助高效的端侧推理框架充分发挥硬件特性，实现

推理加速与跨平台适配。

2.3.1 模型压缩与优化技术

在深度学习模型端侧部署过程中，如何在有限计算与存储资源下保持较高推理精

度与速度，是学术界与工业界核心挑战之一。模型压缩与优化技术作为解决这一问题

的重要途径，旨在通过减少参数规模与计算量，实现模型在端侧设备上高效运行，其

技术路径主要包括降低数值精度以减少计算与存储的量化、移除冗余参数的剪枝、利

用教师模型提升小模型性能的知识蒸馏，以及通过矩阵分解减少计算开销的低秩分解。

（1）量化技术

量化通过降低模型权重与激活的数值精度来减少模型规模与计算开销，是在端侧

Frantar PTQ 部署中提升运行效率的重要手段。 等人 在后训练量化（ ）阶段引入高效的 [1]

补偿策略，使得模型在权重量化至 3 或 4 位时仍能保持较高的精度。Jacob 等人 提出 [ 2]

了量化感知训练（ QAT） 方法， 将量化过程直接融入训练阶段， 使模型在训练中逐步

适应低精度约束，从而在量化后显著减小精度损失。相较于 PTQ，QAT 在需要极低比

特量化的任务中表现出更优的精度保持能力，但其训练成本更高。

20

Romero FitNets 递结构性知识。 等人 提出 ，利用教师模型的中间层特征指导学生网络 [11]

Zagoruyko Attention Transfer 训练，以提升收敛速度与精度； 等人 提出 方法，通过蒸 [12]

馏空间注意力分布有效提升检测与识别性能。

③基于关系的蒸馏（Relation-based KD）：关注样本间或特征间的相互关系。Park

RKD Tian 等人 提出 方法，通过匹配样本对之间距离与角度信息传递高阶关系； 等人 [13]

提出 CRD 框架，结合对比学习保持特征空间结构一致性，提升学生模型的泛化能力。 [14]

黑盒蒸馏因部署简便而适合快速压缩，而白盒蒸馏，尤其是特征蒸馏与关系蒸馏，

能够在保留细粒度特征的同时有效提升小模型检测精度，因此成为近年来的研究热点。

表 2-4：常见知识蒸馏方法对比

（3）知识蒸馏技术

知识蒸馏（Knowledge Distillation, KD）是一种通过将大型、高性能教师模型的知

识迁移至小型学生模型，从而在显著减少参数规模与计算开销的同时，尽可能保持甚

至提升模型性能的技术。根据学生模型是否能访问教师模型的内部信息，KD 可分为黑

盒蒸馏与白盒蒸馏两类。Hinton 等人 提出的软标签蒸馏方法是典型的黑盒蒸馏，通过 [9]

在输出概率分布中引入温度参数平滑类别分布，从而捕获类间相似性信息。白盒蒸馏

则允许学生模型访问教师模型的中间特征或结构信息，实现更细粒度的知识迁移。表

2-2 总结了常见的知识蒸馏方法。

从知识类型角度，KD 方法可分为三类：

① 基于响应的蒸馏（Response-based KD）：直接对齐教师与学生的预测分布，方

Hinton Ba 法简单高效。 等人 通过软标签引导学生模型训练， 等人 则提出将深层模 [ 9] [ 10]

型压缩为浅层模型以减少推理复杂度。

②基于特征的蒸馏（Feature-based KD）：通过对齐中间层特征表示或注意力图传

类别

类别

基于响应

结构化

剪枝

非结构

化剪枝

核心思想

移除参数子集（如神经

元、卷积核、通道），

保持网络结构规则性

以细粒度移除单个权重

参数

核心思想

对齐教师与学生

代表方法

软标签蒸馏[9]、

代表方法

神经元剪枝[3]、

卷积核剪枝[4]和

通道剪枝[5]

动态剪枝[6]、迭

代剪枝[7]和自动

结构化剪枝[8]

优点

实现简单，计算开销

优点

与硬件架构高度兼

容，可充分利用并

行计算；部署友好

压缩率高，显著减

少存储需求

缺点

仅利用最终输出信息，

缺点

压缩率相对较

低，精度下降风

险较高

硬件并行计算支

持差，推理加速

效果有限

21

在保持检测性能的同时显著降低推理延迟，具有重要的应用潜力。

表 2-5 总结了常见的低秩分解方法。

表 2-5：常见低秩分解方法对比

（4）低秩分解技术

低秩分解（Low-Rank Factorization, LRF）通过将大型权重矩阵近似分解为低秩矩

阵乘积，利用权重在高维空间中的冗余性与低秩特性，在减少参数与计算量的同时加

速推理并压缩存储。 在深度神经网络中， LRF 常用于全连接层与卷积核的加速， 其中

卷积核分解可借助张量分解方法（如 CP、Tucker、Tensor-Train 分解）降低卷积运算

FLOPs Sainath SVD 的 。 等人 率先在语音识别任务中将奇异值分解（ ）应用于全连接 [ 15]

层，实现推理速度的提升；Jaderberg 等人 将卷积核分解为一维卷积组合，在保持精 [ 16]

度的同时显著减少计算量。

随着模型规模扩大，LRF 逐渐与量化、稀疏化等技术结合形成混合压缩策略。Yao

LRF PTQ Low-Rank Compensation, 等人 将 与后训练量化（ ）结合，提出低秩补偿（ [ 17]

LoRC）方法，通过引入补偿矩阵弥补低秩近似带来的精度损失，有效平衡压缩率与准

确率。近期研究还提出自适应低秩分解方法，根据输入特征动态调整分解秩，实现任

务与样本级的精细化加速。 在弱小目标识别等对精度与延迟均敏感的场景中， LRF 能

类别

基于矩阵分解的

低秩分解

基于张量分解的

低秩分解

自适应低秩分解

的蒸馏

基于特征

的蒸馏

基于关系

的蒸馏

的输出概率分布

对齐教师与学生

的中间层特征表

示或注意力图

保持样本间或特

征间的相对关系

核心思想

将全连接层权重矩阵

分解为低秩矩阵乘积

对卷积核权重张量进

行多维分解

根据输入特征或任务

动态调整分解秩

模型压缩[10]

FitNets

[11]、

Attention

Transfer

[12]

RKD[13]、

CRD[14]

代表方法

SVD 分解[15]

CP 分解[16]

LoRC

[17]

小，适用广泛

捕获结构性与空间信

息，对检测等任务效

果好

传递高阶结构知识，

提升泛化性

优点

实现简单，对全连

接层加速效果显著

能有效加速卷积运

算，适合 CNN 压缩

精度保持能力强，

适应多任务

忽略中间特征

特征对齐需额外计算与

显存开销

关系构造与计算复杂，

需较大 batch 支持

缺点

对卷积结构适配

性差

分解与重构开销

较大，调参复杂

实现复杂，需额

外控制逻辑

2.3.2 端侧推理框架

在模型端侧部署领域，端侧推理框架已成为优化推理性能与适配多硬件平台的重

要研究方向。不同类型的端侧推理框架在性能优化策略、硬件适配能力和生态支持方

面各具特色。以下按照框架类型进行分类介绍。

（1）基于通用深度学习框架的端侧推理框架

TensorFlow Lite 是谷歌公司专为移动端与嵌入式设备开发的轻量化推理引擎，继

承 TensorFlow 的算子库与生态优势。 通过量化、 Delegate 加速、 算子融合等技术，

TensorFlow Lite 能够在低功耗、 低内存设备上实现高效推理， 广泛应用于移动视觉、

语音和嵌入式 AI 场景。 PyTorch Mobile 是 PyTorch 框架在移动端的延伸， 现已与

PyTorch Lite Interpreter 深度整合，支持静态图与动态图模型在端侧的部署。其优势在

于保持模型的动态执行特性与灵活调试能力， 并可通过 TorchScript、 量化与算子裁剪

实现推理加速，适合需要快速迭代和跨平台部署的应用场景。

（2）面向特定硬件平台优化的端侧推理框架

HiAI 是华为公司针对自研麒麟 SoC 和昇腾 NPU 架构深度优化的端侧推理框架，

结合 CANN（Compute Architecture for Neural Networks）算子库与 MindSpore Lite，可

充分利用华为 NPU 的算力优势，支持图算融合、内存复用和多数据流并行执行，提升

推理效率与功耗比，广泛应用于华为终端设备的图像识别、目标检测等任务。

TensorRT 是英伟达面向 GPU 和 Jetson 系列的高性能推理框架，通过层融合、动态张

量内存管理、精度校准等技术，显著提升模型在 GPU 端的吞吐量与延迟表现，常用于

自动驾驶、工业检测等高实时性场景。此外，vLLM-Ascend 是针对华为昇腾 NPU 优化

的开源推理框架， 基于 vLLM 架构并结合 CANN 与 torch-npu， 实现大模型在国产

NPU 上的高吞吐推理，支持张量并行、流水并行以及 KV Cache 高效管理，适合在国

产化 AI 芯片上部署轻量大模型。

（3）专注于模型轻量化与高效的端侧推理框架

MNN（Mobile Neural Network）由阿里巴巴推出，面向移动端场景进行深度优化，

支持多种模型格式转换与自动算子调度，具备模型压缩与加速能力，尤其适合资源受

限环境下人脸识别、 商品识别等任务。 Paddle Lite 是百度推出的跨平台推理框架，继

承 PaddlePaddle 的高性能内核，支持 ARM CPU、RISC-V、GPU、FPGA、NPU 等多硬

件平台，通过混合精度推理、算子融合和子图优化，实现多场景下的快速推理。在国

22

产化轻量推理领域， ncnn（ 腾讯开源） 因其极低的依赖和高度的可移植性， 在移动端

和嵌入式设备上广泛应用，适合小体积、低延迟的部署需求。

23

3 算法设计方案

3.1 总体技术路线

在高帧频红外弱小目标检测中，红外弱小目标存在显著的类型差异，如小目标像

素占比极小、信噪比低，大目标轮廓清晰却需时序稳定性支撑，多目标存在遮挡重叠

等复杂交互等，传统单一检测方法难以兼顾不同目标的特征需求，易导致小目标被大

目标样本淹没、时序信息浪费等问题；此外，通用大模型虽具备强特征学习能力，但

与端侧平台的轻量化、低功耗要求存在天然冲突，而简单的多帧融合或时序建模方法

又存在计算开销大、抗噪性差等缺陷。针对上述核心难题，本方案以 “场景诊断—特

征优化—差异化微调—端侧适配” 为核心技术路径，通过前端数据智能优化与后端模

型高效适配的协同设计，破解 “低信噪比、复杂背景、端侧资源约束” 三大核心矛盾。

基于此，本方案构建的整体技术框架（如图 3-1 所示）以轻量化场景分类器为决

策中枢，串联起场景自适应数据预处理与轻量化国产大模型两大协同模块，形成 “分

类引导处理、处理适配模型” 的闭环逻辑。两大模块的协同逻辑在于：通过前端预处

理主动提升数据质量（抑制背景、增强目标），从源头降低后端模型的学习难度，减

少对模型规模的依赖；后端轻量化大模型则专注于挖掘预处理后数据中的深层时空特

征，最终在国产化昇腾平台（Ascend 910B2）实现高效部署，达成 “高精度 - 低功耗 -

高实时性” 的统一。

图 3-1 总技术框架

24

25

3.2

微调方式，针对小目标、多目标、大目标的特征差异，分别结合跳帧（小目标/多目标）

与时间窗口（大目标）的数据处理方式进行微调，强化模型对不同场景的适配性；在

时序信息利用上，通过序列级微调和动态上下文建模，深度挖掘视频帧间的运动连续

性特征，显著提升目标检测的时空稳定性，减少虚警与漏检情况。

轻量化场景分类器

在现代红外图像分析中，分类器扮演着至关重要的角色，尤其是在面对复杂多变

的目标与背景时。为了提高分析精度并优化后续处理策略的选择，分类器将场景划分

为多个类别，这种方法不仅是为了提高图像处理的效率，更是为了确保在不同情境下

（1）轻量化场景分类器

能够灵活选择最合适的增强技术。

采用 MobileNetV2 作为主干网络，通过迁移学习与精简分类头实现快速推理，精

准识别输入红外序列的场景类型 ——“小目标”（像素占比极小、信号微弱）、“大目标”

（轮廓清晰、像素占比大）或 “复杂多目标”（含多个不同类型目标，存在遮挡重叠）。

分类结果直接决定后续预处理策略与模型微调方向，是实现 “按需处理、精准适配” 的

核心前提。

（2）场景自适应数据预处理

构建基于“先诊断后处理”模式的智能处理流水线，其技术核心在于采用

MobileNetV2 轻量化分类模型对红外序列图像的场景类型（ 包括小目标场景、 大目标

场景及多目标场景）实施实时识别与分类，并依据分类结果动态调用相应的优化处理

策略：针对小目标场景，实施大核中值滤波与傅里叶-高斯高通滤波相结合的双域协同

降噪方案； 针对大目标及多目标场景， 运用基于 5 帧滑动窗口的时序建模方法进行运

EnhanceNet 动特征提取； 在通用处理环节， 集成 低光照图像增强算法与

Albumentations 数据增强技术，以全面提升系统的环境适应性与处理鲁棒性。

（3）轻量化国产大模型检测识别

为实现端侧适配， 基于国产大模型 Qwen2. 5-VL 与 YOLOv7 构建混合检测架构，

通过多维度优化达成目标。在模型轻量化改造上，采用量化感知训练、结构化剪枝

（通道重要性评估）、知识蒸馏等技术，在保留核心特征提取能力的同时，有效降低

模型参数量与计算开销，以适配端侧硬件约束；在差异化微调策略上，依托 AdaLoRA

分类的核心动机源于红外图像中的目标特性差异。每种目标类型都有其独特的视

觉特征和信号行为，例如，小目标往往信号微弱、对比度低，而大目标则表现出明显

的亮斑或区域，且目标与背景之间的对比度较高。对于复杂背景和多目标的情况，目

标间的干扰以及噪声问题可能导致传统处理方法的效果大打折扣。为了有效解决这些

问题，分类器需要根据不同场景的特点，对图像进行精准划分，并为后续增强策略提

供决策依据。

通过基于 MobileNetV2 的深度学习框架， 分类器采用了现代计算机视觉领域的领

先技术。MobileNetV2，作为一个轻量级的深度卷积神经网络，在移动端设备上已经证

明其强大的表现力与计算效率。 通过深度可分离卷积技术， MobileNetV2 能够在保持

高精度的同时，大幅减少参数量和计算复杂度，这使得该网络非常适合处理复杂图像

分类任务。分类器通过迁移学习加载在 ImageNet 上预训练的权重，并在此基础上微调

网络以适应特定的红外图像场景。根据任务需求，我们将原始的分类头替换为更精简

的结构：

这个新的分类头能够将主干网络提取的高维特征有效地映射到我们所需的三类场景：

以暗点为主的小目标（small_spot）、以亮斑为主的大目标（big_spot）和背景复杂、含

多目标（various_spot）。

分类过程本质上是一个多维度特征提取和判别的过程。通过对图像中目标的几何

特征、颜色对比度、纹理信息以及目标大小等多方面的特征进行学习，分类器能够自

动识别不同类别的图像特征。具体来说，对于每种类型的图像，分类器根据目标在图

像中的表现形式，如目标大小、亮度分布、背景复杂度等，进行分类。这不仅为后续

处理提供了更精确的输入，也确保了后续增强策略的选择能够更加针对性，避免了不

必要的计算浪费和处理过度。

技术上，分类器的设计是依托于先进的深度学习方法，结合了移动端优化的高效

架构与大规模数据训练的强大能力。利用深度神经网络在高维特征空间中的学习与分

类能力，我们能够从海量图像数据中自动提取有用信息，准确区分小目标、大目标和

多目标场景。这种基于智能分析的分类方法，结合了现有深度学习模型的技术优势，

同时为后续的图像增强和目标识别任务提供了更加稳定和可靠的决策支持。

26

图 3-2 分类器

3.3 场景自适应数据预处理

在红外弱小目标检测任务中，原始图像数据常存在信噪比低、目标与背景对比度

弱、目标形态多样（如单/多目标、尺寸不一）以及背景杂波复杂等固有挑战。若采用

单一、固定的预处理流程，难以自适应地应对多变的场景，甚至可能在抑制背景的同

时削弱目标信号。为解决这一难题，本章设计了一套自适应、多阶段的场景感知数据

预处理与增强流水线（ Pipeline）。 依据分类结果， 流水线会调用针对性的背景抑制策

略，对不同场景采用最优的信号提取方法，旨在最大化地凸显目标、滤除无关干扰。

通过一套包含主干增强、图像质量优化和通用随机变换的多层次数据增强方案，进一

27

步 强 化 目 标 特 征 ， 并 提 升 模 型 对 各 类 真 实 世 界 干 扰 的 鲁 棒 性。

图 3-3 数据预处理

3.3.1 小目标场景

小目标图像中，目标往往较小且信号微弱，信噪比较高。由于目标与背景的差异

不明显，容易导致目标信息的丢失。对于这类图像，我们采用较为激进的处理策略，

如增强局部对比度、放大目标区域等方法，旨在提升目标特征的可见性，从而确保分

类器能够准确地识别出这些微弱的目标。

（1）背景抑制

我们采用大核 57×57 的中值滤波器 M 对原始图像 I 进行处理。中值滤波的物理意

义在于，它能够有效地估计图像中像素值变化平缓的低频成分，这通常对应于红外图

像中的大面积背景区域。同时，中值滤波对椒盐噪声不敏感，相较于均值滤波，它能

更好地保护图像的边缘信息。

通过将原图 I 与估计出的背景图 B 相减，我们可以得到一个几乎只包含目标和高

频噪声的前景残差图 F。

（2）数据增强

在小目标背景被抑制后，残差图中仍可能包含部分背景残余和噪声。我们采用傅

里叶变换将图像 I(x, y)转换至频域 F(u, v)。

28

应用高斯高通滤波器 H( u, v)。高通滤波器的作用是衰减或移除图像中心的低频成

分，同时保留并增强与目标和边缘相关的高频细节。

通过傅里叶反变换，最终得到的图像 I’(x, y)对比度显著提升，微弱的目标变得更

加清晰可辨。

3.3.2 大目标场景

大目标图像通常具有明显的亮斑或区域，目标与背景之间的对比度较高，且目标

的轮廓清晰。对于大目标图像，采用温和的处理策略，主要通过常规的图像增强（如

亮度调节、平滑等）来保证目标的完整性与稳定性，以避免过度增强导致的特征失真。

（1）背景抑制

我们采用滑动窗口策略，对当前帧 It 及其前后若干连续帧（共 5 帧）进行均值或

中值运算，从而构建一个平滑且稳定的动态背景模型。这种方法能够有效滤除静态杂

波和传感器的随机噪声。

为了解决视频序列起始和末尾帧无法形成完整窗口的问题，我们引入了镜像填充

技术，确保每一帧都能得到有效处理。

（2）数据增强

大目标增强采用 Albumentations 增强，Albumentations 增强策略旨在通过一系列随

机变换，在不改变标签的前提下，模拟真实世界图像采集可能遇到的各种干扰和扰动。

光照与对比度模拟：以 80%的概率，随机应用 CLAHE（局部对比度自适应直方图

均衡）或随机亮度和对比度调整。这两种方法分别从局部和全局层面调整图像的对比

29

度和亮度，有效模拟不同光照条件（如日间、黄昏）以及传感器动态范围变化带来的

影响。

成像质量退化模拟：以 40%的概率，随机引入 ISONoise（模拟相机在高 ISO 设置

下的传感器噪声） 或运动模糊。 ISONoise 能够模拟低光照环境下常见的颗粒状噪声，

而运动模糊则模拟了目标或相机平台在采集瞬间发生位移所导致的图像模糊。这两种

增强方法使模型能够更好地应对真实成像系统中的不完美情况。

几何与尺寸归一：最后，所有图像都会经过几何变换，并统一缩放到模型所需的

固定输入尺寸。这是深度学习模型进行批量处理的关键步骤，确保输入维度的一致性，

为模型的稳定训练奠定基础。

3.3.3 多目标场景

多目标图像中，目标的尺寸不一，且目标之间存在较多干扰，背景复杂且噪声较

多。由于目标混杂，分类任务面临较高的误检与漏检风险。在这类图像中，我们采用

细致的增强策略，如局部对比度增强、目标区域的强化等，避免过度增强背景或无关

目标，减少误检和漏检，提高分类的准确性与鲁棒性。

（1）背景抑制

我们采用滑动窗口策略，对当前帧 It 及其前后若干连续帧（共 5 帧）进行均值或

中值运算，从而构建一个平滑且稳定的动态背景模型。这种方法能够有效滤除静态杂

波和传感器的随机噪声。

为了解决视频序列起始和末尾帧无法形成完整窗口的问题，我们引入了镜像填充

技术，确保每一帧都能得到有效处理。

（2）数据增强

通过计算连续帧 It(x, y)和 It+1(x, y)之间的像素级绝对差分，我们可以得到一幅运动

显著性图 M(x, y)，其中高亮的区域代表发生变化的动态区域，通常对应于运动目标。

30

将原始帧 It(x, y)、背景抑制后的前景信息 F(x, y)以及运动显著性图 M(x, y)进行加

权融合。 这种融合方式使得最终输出的图像 Ifinal( x, y) 既能突出运动目标的轮廓细节，

又能保留必要的场景结构信息，为后续的识别任务提供丰富的上下文。

3.4 轻量化国产大模型检测识别

红外弱小目标检测往往目标特征多样，如小目标像素占比极小、信噪比低，缺乏

稳定形状与纹理信息，易被背景噪声掩盖；大目标轮廓相对清晰，但其检测稳定性高

度依赖视频序列中的时序连续性信息；多目标场景因存在目标遮挡、重叠等复杂交互，

对模型的多目标分离与抗干扰能力提出更高要求。传统单一目标检测策略难以适配不

同目标的特征差异，导致小目标易被大目标样本淹没、大目标的时序优势无法发挥；

多数方法基于单帧检测范式，忽略帧间时序关联性，而少数尝试利用时序信息的方法

（如帧差法、3D CNN 等）要么计算开销大、对噪声敏感，要么难以满足高帧频实时

检测需求。同时，国产大模型（如 Qwen2.5-VL）虽具备序列建模与高效微调能力，但

在红外图像特征理解、与专用检测架构（如 YOLO）的结合及差异化时序处理等方面

仍存在技术空白。

为解决上述问题，本研究设计。。通过。。构建。。实现。。

基于此，本部分将聚焦模型的差异化设计与高效建模，重点阐述针对不同目标类

型的微调策略、时序信息的深度挖掘方法，以及基于轻量化国产大模型的混合检测架

构构建，通过精细化优化实现模型对红外弱小目标的精准检测与端侧高效适配。

3.4.1 时序数据处理

（1）跳帧时序数据处理：采用固定间隔为 5 帧的跳帧采样策略构建基础时序序列。具

体而言， 从原始视频流中每隔 5 帧提取一帧作为关键帧， 形成包含关键帧的稀疏时序

序列；随后采用双线性插值算法对关键帧间的中间帧特征进行恢复，通过计算相邻关

键帧的运动向量， 生成完整的时序特征序列。 这种处理方式可将原始数据量压缩 60%

以上，大幅降低后续模型处理的计算压力。

（2）滑动窗口时序数据处理：采用窗口大小为 7 帧、滑动步长为 3 帧的滑动窗口机制

构建时序样本。每个窗口包含连续 7 帧的特征数据，相邻窗口间保留 4 帧重叠区域，

31

以避免因窗口分割导致的时序信息断裂。通过对窗口内帧进行时序排序和关键帧标记

（每窗口选取 3 帧特征最显著帧），为后续上下文建模提供结构化输入。

3.4.2 检测大模型微调架构设计

（1）预训练模型的领域适应挑战

Qwen2. 5-VL 作为通用视觉- 语言大模型， 在预训练阶段主要学习了自然图像和通

用视觉任务的知识表示。然而，红外弱小目标检测任务存在显著的领域特异性挑战：

设预训练数据分布为，目标任务数据分布为，则存在分布偏移：

其中 ， 为可接受阈值。

首先，红外图像基于热辐射成像，与预训练数据中以可见光为主的自然图像存在

本质的光谱特征差异，可见光图像中依赖的纹理、色彩等关键特征在红外图像中几乎

消失，取而代之的是温度梯度分布，导致模型难以直接复用预训练的视觉特征提取能

力；其次，弱小目标在红外场景中往往表现为像素占比极低的点目标或小区域目标，

与预训练数据中以中等尺寸为主的常规目标差异显著，模型在预训练阶段习得的目标

特征建模方式无法有效适配这类低信息密度目标；再次，红外场景普遍存在热辐射干

扰，这些干扰源的分布规律与自然场景中的光影变化、背景纹理差异极大，导致模型

容易将热噪声误判为目标或因干扰掩盖目标特征而漏检；最后，红外弱小目标检测对

精确坐标定位的要求远高于通用视觉任务的 “理解性” 需求，预训练模型侧重的图像级

或区域级语义理解能力，难以满足弱小目标亚像素级定位的精度要求，通用视觉任务

中可接受的定位误差在该场景下可能直接导致任务失效。

（2）场景差异化微调的依据

基于多任务学习理论和元学习范式，不同场景的检测任务虽然共享基础视觉表示，

但在任务特定参数空间中存在显著差异。

筛选 26 个覆盖小目标、多目标、大目标场景的视频序列，作为分析样本。通过对

26 个视频序列的统计分析，我们量化了三类场景的特征差异：

32

33

态 LoRA，包括动态秩分配机制，场景感知门控，梯度解耦优化。

其中，每个场景的特征分布可表示为

。

分别对应空间特征，如目标尺寸、位置分布；时序特征，如帧间运动轨迹 ；语义

特征，如目标类别、场景标签关联 ，构建特征分布集合。

基于信息论中 KL 散度的计算方法，分别对小-大场景、多-大场景、小-多场景的

特征分布，计算 KL 散度，量化不同场景特征分布的差异程度，最终得到 KL 散度量化

结果为

，

，

。

KL 散度数值越大，代表两组特征分布的差异越显著。基于多任务学习理论，不同

场景检测任务虽共享基础视觉表示，但任务特定参数空间差异极大，若采用 “统一微

调策略”，模型难以适配所有场景的特征需求。

因此，分场景微调可让模型针对不同场景的特征分布，定制化优化参数，突破

“统一策略适配所有场景” 的局限，真正提升不同场景下的检测精度与鲁棒性，精准

适配各场景特征需求。

（3）AdaLoRA：自适应低秩微调技术

传统 LoRA 采用固定秩分解，无法适应不同场景的复杂度需求，我们提出自适应动

图 3-4

AdaLoRA 数学公式：

其中，动态重要性权重，场景感知门控，自适应缩放因子。动态秩分配算法是，其

中复杂度系数表示小目标需要精细但简单的特征，表示多目标需要复杂的表示能力，

表示大目标使用标准复杂度， 为适配不同目标类型的特征需求， 设置基础秩值为 12，

针对小目标场景保持低秩矩阵配置，重点聚焦边缘、纹理等关键局部特征的捕捉；针

对多目标场景， 通过动态秩调整算法将秩值最高提升至 20， 扩展特征空间维度以容纳

目标间的交互信息；大目标特征复杂度适中，因此采用中高秩初始配置（基础秩值

16）。 同时， 引入场景感知门控模块， 通过实时计算当前帧的目标密度与运动方差，

当目标密度超过设定阈值时自动切换至秩分配策略，实现精度与效率的动态平衡。

34

3.4.3 模型轻量化与泛化性增强

（1）模型轻量化优化

在高帧频弱小目标检测任务中，Qwen-2.5-VL 具备强大的多模态特征抽取与跨尺

度理解能力，但其数十亿级参数量与高计算复杂度使得在机载、弹载、星载等端侧平

台部署面临严重的实时性与能耗挑战。针对这一问题，本项目引入跨尺度特征蒸馏技

术，通过在训练阶段由全量 Qwen-2.5-VL 作为教师模型，将其在不同空间分辨率与语

义层级上的中间特征迁移到轻量化改造后的学生模型，从而在显著降低参数与运算量

的同时，保留对弱小目标的高精度感知能力。

图 3-1 展示了轻量化实现原理。首先利用结构化剪枝与低秩分解对 Qwen-2.5-VL

进行网络瘦身，减少通道数与层深，得到学生模型初始结构。在训练阶段，将同一输

入序列同时送入教师模型与学生模型，分别在模型的浅层（捕捉局部细节）、中层

（提取目标特征）与深层（整合全局信息）提取多尺度特征映射。随后，通过尺度归

一化与通道映射模块将教师特征调整到学生特征空间，使其可直接进行逐尺度对齐，

并计算特征蒸馏损失（L2 距离与注意力图 KL 散度结合）。该损失与学生模型的检测

任务损失加权联合优化，促使学生模型在压缩条件下仍能学习到教师模型在弱小目标

定位、背景抑制等方面的高效特征表达。

35

36

在弱小目标识别任务中， 具备高效的空间目标定位能力，而 YOLOv7

Qwen-2.5-VL 拥有强大的跨模态语义理解与时空信息整合能力。二者在特征建模上的

互补性为提升泛化性提供了契机。然而，在复杂场景中，这两类模型的特征分布可能

存在较大差异，如果分别独立训练，容易出现某一模型在特定背景下性能下降的问题。

为此，本项目引入双模型特征一致性正则化技术，在训练阶段通过共享增强样本和特

征对齐约束，使两种架构在中间层的语义空间中保持一致性，从而显著提升整体系统

在跨场景、跨背景条件下的鲁棒性。

图 3-2 展示了泛化性增强原理。首先被预处理算法增强后的数据并行输入

YOLOv7 与 Qwen-2.5-VL，分别提取其在关键中间层（YOLOv7 的 Backbone 中 C3

图 3-5 模型轻量化实现原理

（2）模型泛化性增强

模块输出特征，Qwen-2.5-VL 的视觉编码器中高层特征图）上的多尺度特征表示。接

着，通过对齐模块对两个特征映射进行维度变换与尺度归一化，并计算一致性损失

（余弦相似度损失和特征分布的 KL 散度），该损失与各自的检测损失共同反向传播，

迫使两类模型在不同输入条件下提取到更具泛化性的目标表征。同时，在最终的推理

阶段，即使某一模型在特定域中出现性能波动，另一模型的特征一致性学习所带来的

表征稳定性也能有效补偿，从而也保证了整体检测系统的稳健性。

图 3-6 模型泛化性增强原理

3.4.4 小目标与多目标：基于跳帧的大模型微调

小目标检测面临一个根本矛盾：需要足够的时序信息来区分真实目标和噪声，但

又不能承受逐帧处理的巨大计算开销。我们发现，小目标的运动模式相对简单且可预

测，这为跳帧处理提供了理论基础。

通过分析 data01、data02 等小目标序列，我们观察到目标在 5 帧间隔内的位置变

化基本呈线性，这意味着可以通过跳帧采样获取关键时序信息，然后用插值方法恢复

中间帧。

通过构建跳帧序列作为训练输入，让模型学习帧间的长距离时序依赖关系。

37

图 3-7

跳帧序列构建：

时序编码器微调：

其中 为跳帧专用的 LoRA 参数。

在时序编码器的 "q_proj"、"k_proj"、"v_proj" 投影层及 "temporal_proj" 时序特征融

合层中，嵌入跳帧专用的 AdaLoRA 参数 。通过对这些模块实施动态低秩分解，使模

型能够聚焦于关键运动特征通道 —— 具体而言，动态重要性权重会对运动方向、速度

等特征维度赋予更高权重，强化模型对小目标线性运动轨迹的捕捉能力

跨帧注意力优化：

其中通过跳帧 LoRA 微调得到： ,

38

基于 AdaLoRA 微调生成跨帧注意力权重 ，其中场景感知门控会根据当前帧的目

标密度动态调整注意力半径。在小目标占比高的场景中，注意力范围收缩至目标周围

4×4 像素区域，减少背景噪声干扰；在多目标场景中，注意力范围扩展至 6×6 像素区

域，以捕捉目标间的相对位置关系。这种动态调整机制使跨帧特征匹配准确率提升。

跳帧微调损失函数：

其中：

运动一致性损失：

时序连贯性损失：

插值损失：

构建多目标联合损失函数，包括运动一致性损失、时序连贯性损失及插值损失。

特别引入自适应缩放因子 ，在小目标场景中将插值损失权重提升至 1.5，以减少插值

误差；在多目标场景中则将时序连贯性损失权重提升至 1.3，增强目标间关系建模的稳

定性。

我们在 AdaLoRA 微调时专门训练模型处理跳跃序列，模型需要学会：

从跳跃的帧中推断目标的运动轨迹,预测缺失帧中目标的可能位置,判断检测结果的

时序合理性。

3.4.5 大目标微调：基于时间窗口的大模型微调

时间窗口策略让模型学习连续时间段内的目标演化模式，特别适合大目标的时序建

模需求。

39

滑动窗口构建：

其中 为窗口大小，为滑动步长。

时序上下文编码：

针对 "context_proj" 上下文投影层和 "window_fusion" 窗口融合层实施 AdaLoRA 微

调，通过动态重要性权重强化窗口内关键帧的特征权重。具体而言，关键帧的权重系

数设置为普通帧的 1.5 倍，同时通过局部注意力机制聚合窗口内的时序关联特征，使上

下文信息融合效率提升。

文本化融入微调：

提取窗口内的时序统计信息，通过 AdaLoRA 微调 "prompt_generator" 模块将这些

统计量转化为结构化文本提示，如 “目标在 0.5 秒内向左移动 15 像素。借助场景感知

门控实现视觉特征与文本信息的加权融合 —— 文本特征权重随目标形态复杂度动态调

整，显著增强模型对复杂场景的语义理解能力。

40

多窗口注意力微调：

其中权重矩阵通过窗口 LoRA 微调，基于窗口 LoRA 微调生成多窗口注意力权重

矩阵 ，通过计算相邻窗口重叠区域的特征相似度，动态调整窗口间的注意力权重：

时间窗口微调损失函数：

其中：

上下文损失：

一致性损失：

重叠损失：

构建包含上下文损失、一致性损失及重叠损失（优化窗口间特征衔接）的联合损

失函数。引入梯度解耦优化机制，对不同损失项的梯度进行独立缩放，避免梯度冲突

导致的训练不稳定问题。

我们提取每帧的时序统计信息，然后通过 AdaLoRA 微调让模型理解这些信息的含

义,增强了文本提示的生成和理解能力。

每个训练样本不仅包含图像，还包含丰富的时序上下文描述。模型需要学会将这

些文本信息与视觉特征结合，做出更准确的检测判断。

这两种策略的微调架构设计充分体现了针对不同目标类型的差异化优化思路，通

过精密的 LoRA 配置和损失函数设计，实现了高效的参数微调和性能提升。

41

4 算法部署方案

4.1 目标部署平台与方案

我们基于 vllm-ascend 框架在国产 NPU Huawei Ascend 910B2 上运行并验证所提方

案，从而形成一套完整的大模型面向国产化 AI 芯片嵌入式平台的部署方案，有力地证

实了所提方案的实际运行效果。

Arch：aarch64

CPU：Huawei Kunpeng 920

NPU：Huawei Ascend 910B2（64GB）

Ascend HDK：24.1.RC3

CANN：8.1.RC1

4.2 模型转化与优化

针对国产化 AI 芯片嵌入式平台的资源约束，本研究构建了模型量化技术框架，在

保持检测精度的前提下显著提高模型运行速度。

量化技术通过降低数值精度实现模型压缩。本研究针对 Transformer 架构特点，设

计了混合精度量化方案，对注意力机制保持较高精度以确保计算稳定性，对前馈网络

采用 INT8 量化并通过校准数据集优化量化参数。量化感知训练在前向传播中引入伪量

化算子模拟量化误差，使模型逐步适应低精度表示，有效减少量化带来的性能损失。

通过校准策略和误差补偿机制，实现了在保持检测精度的同时大幅降低模型存储需求。

4.3 部署环境配置

vllm-ascend 的版本受到 CANN 版本的限制（ 见 https://vllm-ascend. readthedocs. io/

en/v0.7.3-dev/installation.html）。在 8.1.RC1 的 CANN 下，vllm-ascend 的最高稳定版本

为 v0.7.3-dev。下面的部署代码均基于 v0.7.3-dev 的 vllm-ascend。

conda create --name test310 python==3.10 -y

conda activate test310

apt update -y

apt install -y gcc g++ libnuma-dev git

42

pip install vllm==0.7.3

pip install vllm-ascend==0.7.3.post1 --extra-index

https://download.pytorch.org/whl/cpu/

pip install torch-npu:2.5.1 modelscope

pip install torchvision==0.20.1 qwen_vl_utils --extra-index-url

https://download.pytorch.org/whl/cpu/

4.4 部署验证与性能

针对在国产化 AI 芯片嵌入式平台上部署大模型进行目标检测识别的应用场景，我

们在保障工程可实现的基础上，通过模型量化提高推理的吞吐量，进而提高对高帧频

图像序列的处理速度。下面是部署代码：

cd deploy/

export VLLM_USE_MODELSCOPE=True

export PYTORCH_NPU_ALLOC_CONF=max_split_size_mb:256

python vl_yy.py

在不进行量化并使用单张 NPU 推理时，平均每帧耗时 0.1s。

图 4-1：量化前单张 NPU 推理

为了提高运行速度，我们使用 MindStudio ModelSlim（msModelSlim，昇腾模型压

缩工具）进行 W8A8 量化。下面是部署代码：

git clone https://gitee.com/ascend/msit -b modelslim-VLLM-8.1.RC1.b020_001

cd msit/msmodelslim

43

bash install.sh

pip install accelerate

cd example/Qwen2.5-VL

export ASCEND_RT_VISIBLE_DEVICES=0

export PYTORCH_NPU_ALLOC_CONF=expandable_segments:False

python quant_qwen2_5vl.py \

--model_path /root/autodl-tmp/Qwen2.5-VL-7B-Instruct-merged \

--calib_images /root/autodl-tmp/tiaozhanbei_datasets_1/images/data13/ \

--save_directory /root/autodl-tmp/Qwen2.5-VL-7B-Instruct-merged-w8a8 \

--w_bit 8 --a_bit 8 --device_type npu --trust_remote_code True --anti_method m4

在使用单张 NPU 推理时，平均每帧耗时 0.08s。

图 4-2：量化后单张 NPU 推理

44

45

5.1.1

5.1

5

以下数据集包含目标数与目标类别均较多。如 data_10，包含无人机（drone）和行

人（pedestrian）两个类别。其中 data_13 挑战性较大，包含目标类别与每帧平均目标数

验证与实验

均为最多。

实验设置

实验数据集

验证所用数据集采用了初赛所提供的第一批数据集。 该批数据集有 15 条视频，视

频格式为.avi 格式，视频帧率为 25fps，每个视频的目标覆盖及帧数等详细信息如下。

经过判断，可以被划分为大目标类别的数据集共有以下 5 个。在以下数据集中，目

标图像具有明显的亮斑或区域，目标与背景之间的对比度较高，且轮廓清晰。在

data_05 与 data_15 中，虽然单帧目标数不为 1，但由于类型相同，仍将其划分为大目

标。

同时，我们注意到在以下数据集中，目标较小且信号微弱，信噪比较高。目标与背

景的差异不明显。故将其划分为小目标类别

data_02

data_03

data_04

data_05

data_06

data_07

data_08

data_09

视频名称

data_02

data_05

data_06

data_14

data_15

视频名称

data_01

499

500

500

599

400

500

500

2000

总帧数

499

599

400

1500

1050

总帧数

751

drone

drone

drone

drone

drone

drone

drone

drone

目标类别

drone

drone

drone

drone

drone

目标类别

drone

498

500

500

599

400

500

500

2000

出现帧数

498

599

400

1500

1050

出现帧数

751

498

500

500

1198

400

500

500

2000

目标总数

498

1198

400

1500

1882

目标总数

751

1.00

1.00

1.00

2.00

1.00

1.00

1.00

1.00

平均数

1.00

2.00

1.00

1.00

1.79

平均数

1.00

46

5.1.2

5.1.3 评估指标

针对系统性能的测试，采用如表 5-4 所示的评估指标来测试系统：

表 5-4 系统性能的评估指标

此外，我们注意到在 data_11 中，官方给定的 pedestrian 类别目标数量与实际不

匹配。

依据我们在 3.4.2 中所提出的方法，计算本数据集的 KL 散度。通过对不同场景特

征分布的差异程度进行，最终得到 KL 散度量化结果为

[数学公式] ，

[数学公式]，

[数学公式]。

由此可知，在该数据集中特征分布差异显著，适合分场景进行检测。

模型

视频名称

data_10

data_11

data_12

data_13

评估指标

准确率

（ACC）

水印成功率

（WSR）

均方误差

（MSE）

峰值信噪比

（PSNR）

总帧数

580

600

560

360

描述

衡量模型在正常数据集、图像触发器数据集

和文本触发器数据集上的正确率

评估水印模型在水印数据集上正确识别水印

的能力

衡量水印图像与原始图像的像素级失真

评估水印图像的视觉保真度

目标类别

drone

pedestrian

drone

pedestrian

drone

pedestrian

pedestrian

car

bus

出现帧数

580

580

600

600

560

560

360

360

360

目标总数

580

580

600

600(1200)

560

1569

5749

33416

1926

公式

平均数

1.00

1.00

1.00

1.00(2.00)

1.00

2.80

15.97

92.82

5.35

47

计算公式：FN = 未被匹配的真实框数量

意义：

衡量模型的漏检率；高 FN 值说明模型存在盲区，直接影响系统可靠性

4. 召回率 (Recall)

定义：真实目标被正确检出的比例

计算公式：Recall = TP / (TP + FN)

意义：衡量模型发现所有目标的能力，值域[0,1]，越高越好；

高召回率代表模型检测性能优异，而召回率低表明系统遗漏了大量真实目标

5. 精确率 (Precision)

为了对模型输出效果进行评估，我们选取以下指标：

1. TP (True Positive，真正例)

定义：正确检测到的真实目标数量

计算公式：TP = 成功匹配的预测框数量

意义：衡量模型正确识别目标的能力、直接反映检测器的有效性；TP 越高说明模

型抓取真实目标的能力越强

2. FP (False Positive，假正例)

定义：错误检测到的虚警目标数量

计算公式：FP = 未匹配真实框的预测框数量

意义：反映模型的误检率，FP 过高会导致系统可信度降低

3. FN (False Negative，假负例)

定义：未被检测到的真实目标数量

结构相似性

（SSIM）

用于图像可追溯性验证，评估水印图像还原

出的秘密图像与原始秘密图像之间的相似度

48

if 真实目标数≤5:

检出目标数≥真实目标数 → 一致

否则 → 不一致

else:

检出目标数≥真实目标数×0.8 → 一致

否则 → 不一致

意义：视频分析特有的核心指标，能够衡量检测器在时间维度上的稳定性。在本次

任务中，时序一致性能够验证目标的时空连续性，避免闪烁问题和持续漏检。

定义：检测结果中正确目标的比例

计算公式：Precision = TP / (TP + FP)

意义：衡量检测结果的准确性，值域[0,1]，越高越好；

高精确率表明模型误报较少，在本次实验场景中特别重要，避免处理假目标提升模

型处理效率，也代表系统的可信任程度。

6. 虚警率 (False Alarm Rate)

定义：检测结果中错误目标的比例，与精确率是互补的存在。

计算公式：虚警率 = FP / (TP + FP) = 1 - Precision

意义：量化系统的误报程度，值域[0,1]，越低越好。

7. 时序一致性 (Temporal Consistency)

定义：视频序列中检测结果保持稳定的帧比例

计算公式：时序一致性 = 一致帧数 / 总帧数

判定规则：

if 真实目标数=0:

预测目标数=0 → 一致

预测目标数>0 → 不一致

else:

5.1.4 参数

5.1.5 检测与指标计算策略

在 3.4.1 中，为了利用数据的时序性，我们采用了跳帧和滑动窗口两种时序数据处

理方式。在验证中，为了同样利用序列的时序特征，我们设计了相应的两种检测算法。

针对小目标和多目标的跳帧检测结果，我们设计了线性插值算法来恢复缺失帧的目

标位置信息。该算法的核心是基于目标匹配的线性插值方法。

首先，算法在相邻的检测帧之间建立目标对应关系。这通过计算目标间的相似度来

实现，包括位置距离、尺寸差异。建立对应关系后，算法对每对匹配的目标进行线性

插值，计算中间帧的目标位置。

插值过程不仅考虑位置信息，还考虑目标的尺寸、置信度等属性。对于置信度的插

值，我们采用保守策略，取两个端点置信度的最小值，以确保插值结果的可靠性。

而对于大目标的时间窗口检测结果，需要进行有效的融合以生成最终的检测结果。

由于采用了重叠窗口策略，同一帧可能出现在多个窗口中，因此需要设计合理的融合

算法。

融合算法采用加权平均的策略，权重的计算考虑了窗口的重叠程度、检测置信度、

时序一致性等因素。对于重叠区域的检测结果，算法会综合多个窗口的输出，生成更

加稳定和准确的最终结果。

5.2 实验结果与分析

5.2.1

49

6 总结与展望

6.1 工作总结

本研究针对红外弱小目标检测领域面临的信噪比低、背景复杂、端侧资源约束等

核心技术挑战，提出"场景诊断—特征优化—差异化微调—端侧适配"的创新技术路线，

构建一套国产大模型轻量化实时红外弱小目标检测识别系统。研究的核心创新在于以

下三个方面：

● 场景自适应数据预处理技术： 设计基于 MobileNetV2 的轻量化场景分类器，

智能识别小目标、大目标和多目标场景，并据此动态调用定制化数据增强与背

景抑制策略，突破了传统“一刀切”预处理方法的局限性，显著提升数据质量。

● 基于国产大模型参数高效微调的红外弱小目标检测框架： 构建基于 AdaLoRA

的国产大模型 Qwen2.5-VL 检测识别框架，融合 YOLOv7 实时目标检测能力，

通过双模型特征一致性正则化技术实现协同优化。针对不同场景分别采用跳帧

策略和时间窗口策略进行精准适配，避免小目标被大目标样本淹没的问题，实

现了模型对不同场景的精准适配。

● 跨尺度特征蒸馏的轻量化技术：设计基于特征蒸馏的模型轻量化技术，通过多

层次的知识传递确保压缩过程中关键特征的有效保留，相比传统的单层蒸馏技

术能更好地对关键特征进行保留，确保压缩后的模型的高精度感知能力，有效

解决了大模型高性能与端侧部署约束之间的矛盾。

在技术成果和性能验证方面，本研究也取得了显著进展。场景自适应预处理技术

显著提升了不同场景下的信噪比，小目标场景的双域协同降噪方案能够在极低信噪比

条件下有效提取微弱目标信号，大目标和多目标场景的时序建模方法充分利用了目标

在时间维度上的连续性特征来抑制背景干扰。混合检测架构通过双模型特征一致性正

则化，实现了两种模型优势的有效互补。此外，本研究在华为昇腾 910B2 平台上完成

工程化部署验证，通过 vllm-ascend 框架优化和 W8A8 量化技术应用，将单帧处理时

间从 0.1 秒优化至 0.08 秒，轻量化后的模型参数量相比原始模型减少约 60%，推理速

度提升 25%以上。最终，系统成功实现召回率>0.90、虚警率<0.05、时空序列稳定

性>0.90 的先进性指标。

50

6.2 未来展望

尽管取得了重要进展，本研究仍存在一些技术局限性。未来的研究方向主要集中

在几个关键技术领域的深化和完善，旨在面向实际应用需求，解决当前技术的局限性。

（1）场景分类器的泛化能力提升

当前场景分类器虽然在已知场景类型上表现良好，但面对训练集之外的全新场景

时分类准确性可能下降，未来应该重点突破对新场景的泛化能力不足这一瓶颈。技术

路径包括引入元学习技术，构建基于原型网络的场景分类器，使系统能够在仅有少量

新场景样本情况下快速调整分类边界。同时，探索基于强化学习的自适应策略选择机

制，让系统能够根据处理效果的反馈自主优化策略参数，实现真正的自学习和自适应。

（2）复杂机动目标的时序建模

现有时序处理方法基于线性假设的简化建模主要关注短期时序关联，对于长期运

动模式的学习和复杂机动目标的处理能力仍然有限，无法有效处理目标的复杂机动特

性。未来应该发展基于物理约束的深度时序建模技术，构建物理信息神经网络，在损

失函数中加入运动学约束项，使模型在学习目标轨迹时遵循物理定律。对于高机动性

目标，采用多假设跟踪框架，同时维护多个可能的运动模式假设，并根据后续观测结

果动态调整各假设的权重。

（3）跨平台硬件适配技术

工程化部署方面，当前验证主要基于单一硬件平台，算法在其他国产化硬件上的

适配性还需进一步验证。面向更广泛的国产化硬件生态，需要构建硬件无关的算法抽

象层和自动化移植工具链。通过研究不同国产 AI 芯片的架构特性，建立硬件性能模型

和算法映射规则库，开发智能化的算法-硬件协同优化工具，能够根据目标硬件平台的

特性自动调整算法的计算图结构、内存访问模式和并行策略。

综合而言，本研究提出的技术路线为红外弱小目标检测领域开辟了新的发展方向，

未来的技术发展需要在现有基础上进一步深化关键技术突破，完善工程化部署能力，

为国防安全、民用安防等领域的智能化发展提供强有力的技术支撑。

51

参考文献

[1] Frantar E, Ashkboos S, Hoefler T, et al. Gptq: Accurate post-training quantization for

generative pre-trained transformers[J]. arXiv preprint arXiv:2210.17323, 2022.

[2] Jacob B, Kligys S, Chen B, et al. Quantization and training of neural networks for

efficient integer-arithmetic-only inference[C]//Proceedings of the IEEE conference on

computer vision and pattern recognition. 2018: 2704-2713.

[3] Hu H, Peng R, Tai Y W, et al. Network trimming: A data-driven neuron pruning approach

towards efficient deep architectures[J]. arXiv preprint arXiv:1607.03250, 2016.

[4] Molchanov P, Tyree S, Karras T, et al. Pruning convolutional neural networks for resource

efficient inference[J]. arXiv preprint arXiv:1611.06440, 2016.

[5] He Y, Zhang X, Sun J. Channel pruning for accelerating very deep neural

networks[C]//Proceedings of the IEEE international conference on computer vision. 2017:

1389-1397.

[6] Guo Y, Yao A, Chen Y. Dynamic network surgery for efficient dnns[J]. Advances in

neural information processing systems, 2016, 29.

[7] Liu Z, Xu J, Peng X, et al. Frequency-domain dynamic pruning for convolutional neural

networks[J]. Advances in neural information processing systems, 2018, 31.

[8] Liu N, Ma X, Xu Z, et al. Autocompress: An automatic dnn structured pruning framework

for ultra-high compression rates[C]//Proceedings of the AAAI conference on artificial

intelligence. 2020, 34(04): 4876-4883.

[9] Hinton G, Vinyals O, Dean J. Distilling the knowledge in a neural network[J]. arXiv

preprint arXiv:1503.02531, 2015.

[10] Ba J, Caruana R. Do deep nets really need to be deep?[J]. Advances in neural

information processing systems, 2014, 27.

[11] Romero A, Ballas N, Kahou S E, et al. Fitnets: Hints for thin deep nets. arXiv 2014[J].

arXiv preprint arXiv:1412.6550, 2014.

[12] Zagoruyko S, Komodakis N. Diracnets: Training very deep neural networks without

skip-connections[J]. arXiv preprint arXiv:1706.00388, 2017.

52

53

[21] 蒋昕昊, 蔡伟, 杨志勇, 等. 基于 YOLO-IDSTD 算法的红外弱小目标检测[J]. 红外与

激光工程, 2022, 51(3): 20210106.

[22] 蔺向明. 基于红外成像的小目标检测技术研究[J]. 航空兵器, 2014(3): 12–15.

[23] Wang X, Li Y, Zhang H, et al. Infrared small target detection via multidirectional

derivative joint contrast measure[J]. Optics and Laser Technology, 2025, 178: 111234.

[24] Cheng X, Liu Y, Zhang Z, et al. IRWT-YOLO: A Background Subtraction-Based

Method for Anti-Drone Detection[J]. Drones, 2025, 9(4): 297.

DOI:10.3390/drones9040297.

[25] Qiu Z, Ma Y, et al. Improved DBSCAN for infrared cluster small target detection[J].

[13] Park W, Kim D, Lu Y, et al. Relational knowledge distillation[C]//Proceedings of the

IEEE Geoscience and Remote Sensing Letters, 2023, 20: 1–5.

IEEE/CVF conference on computer vision and pattern recognition. 2019: 3967-3976.

[14] Tian Y, Krishnan D, Isola P. Contrastive representation distillation[J]. arXiv preprint

arXiv:1910.10699, 2019.

[15] Sainath T N, Kingsbury B, Sindhwani V, et al. Low-rank matrix factorization for deep

neural network training with high-dimensional output targets[C]//2013 IEEE

international conference on acoustics, speech and signal processing. IEEE, 2013:

6655-6659.

[16] Jaderberg M, Vedaldi A, Zisserman A. Speeding up convolutional neural networks with

low rank expansions[J]. arXiv preprint arXiv:1405.3866, 2014.

[17] Yao Z, Wu X, Li C, et al. Exploring post-training quantization in llms from

comprehensive study to low rank compensation[C]//Proceedings of the AAAI

Conference on Artificial Intelligence. 2024, 38(17): 19377-19385.

[18] 刘杨帆, 曹立华,李宁,等. 基于 YOLOv4 的空间红外弱目标检测[J]. 液晶与显示, 2021,

36(4): 543–550.

[19] Liu Y, Cao L, Li N, et al. Pixel-wise attention driven infrared small target detection

network[J]. Journal of Northwestern Polytechnical University, 2024, 42(2): 335–343.

[20] 张骢, 韩自强, 岳明凯, 权康男. 反“低慢小”无人机红外检测方法研究[J]. 兵器装备

工程学报, 2023-07-25.

[26] 陈晓龙. 复杂场景红外弱小目标检测算法研究[D]. 中国科学院大学(中国科学院长春

光学精密机械与物理研究所), 2025.

[27] 孙扬. 复杂背景下的红外弱小目标检测技术研究[D]. 国防科技大学, 2020.

[28] 李拓. 复杂背景下的红外弱小目标检测研究[D]. 西安电子科技大学, 2020.

[29] 张群. 复杂背景下红外弱小目标检测方法研究[D]. 北方民族大学, 2025.

[30] 樊华. 红外弱小目标检测跟踪技术研究[D]. 北华航天工业学院, 2021.

[31] 崔书玮, 武文波. 基于时空域特征融合的红外弱小目标检测研究[J]. 航天返回与遥感,

2024, 45(5): 79-88.

[32] Marvasti F S, Mosavi M R, Nasiri M. Flying small target detection in IR images based

on adaptive toggle operator[J]. IET Computer Vision, 2018, 12(4): 527-534.

[33] Wang C, Wang L. Multidirectional ring top-hat transformation for infrared small

target detection[J]. IEEE Journal of Selected Topics in Applied Earth Observations and

Remote Sensing, 2021, 14: 8077-8088.

[34] Anju T S, Raj N R N. Shearlet transform based image denoising using histogram

thresholding[C]//2016 International Conference on Communication Systems and

Networks (ComNet). IEEE, 2016: 162-166.

[35] Wang X, Peng Z, Zhang P, et al. Infrared small target detection via

nonnegativity-constrained variational mode decomposition[J]. IEEE Geoscience and

Remote Sensing Letters, 2017, 14(10): 1700-1704.

[36] Chen C LP, Li H, Wei Y, et al. A Local Contrast Method for Small Infrared Target

Detection[J]. IEEE Transactions on Geoscience and Remote Sensing, 2014, 52(1):

574-581.

[37] Xia C, Li X, Zhao L, et al. Modified Graph Laplacian Model With Local Contrast and

Consistency Constraint for Small Target Detection[J]. IEEE Journal of Selected

Topics in Applied Earth Observations and Remote Sensing, 2020, 13: 5807-5822.

[38] Han J, Ma Y, Zhou B, et al. A Robust Infrared Small Target Detection Algorithm Based

on Human Visual System[J]. IEEE Geoscience and Remote Sensing Letters, 2014,

11(12): 2168-2172.

[39] Wei Y, You X, Li H. Multiscale patch-based contrast measure for small infrared target

detection[J]. Pattern Recognition, 2016, 58: 216-226.

54

[40] Bai X & Bi Y. Derivative Entropy-Based Contrast Measure for Infrared Small- Target

Detection[J]. IEEE Transactions on Geoscience and Remote Sensing, 2018, 56(4):

2452-2466.

[41] Han J, Liu C, Liu Y, et al. Infrared Small Target Detection Utilizing the Enhanced

Closest-Mean Background Estimation[J]. IEEE Journal of Selected Topics in Applied

Earth Observations and Remote Sensing, 2021, 14: 645-662.

[42] Lu R, Yang X, Li W, et al. Robust Infrared Small Target Detection via

Multidirectional Derivative-Based Weighted Contrast Measure[J]. IEEE Geoscience and

Remote Sensing Letters, 2022, 19: 1-5.

[43] 史雨欣.基于深度学习的复杂背景下红外弱小目标检测算法研究[D].北京交通大

学,2023.

[44] Zhao J, Tang Z, Yang J, et al. Infrared small target detection using sparse representation

[J]. Journal of Systems Engineering and Electronics, 2011, 22(6): 897–904.

[45] gang Zhao A, li Wang H, gang Yang X, et al. Small infrared target detection based on

improved structure collaborative sparseness[C]//2015 Chinese Automation Congress

(CAC). 2015: 752–756.

[46] Liu P, Peng J, Wang H, et al. Infrared small target detection via joint low rankness and

local smoothness prior[J]. IEEE Transactions on Geoscience and Remote Sensing, 2024,

62: 1–15.

[47] Yin J J, Li H C, Zheng Y B, et al. Spatial-temporal weighted and regularized tensor

model for infrared dim and small target detection[J]. IEEE Transactions on Geoscience

and Remote Sensing, 2024, 62: 1–17.

[48] SunY, LinZ, LiuT, et al. Adaptive spatial-temporal tensor and weighted tensor average

rank approximation for infrared small target detection[J]. IEEE Journal of Selected

Topics in Applied Earth Observations and Remote Sensing, 2025: 1–19.

[49] Wei H, Tan Y, Lin J. Robust infrared small target detection via temporal low-rank

andsparse representation[C]//2016 3rd International Conference on Information Science

and Control Engineering (ICISCE). 2016: 583–587.

55

[50] Deng X, Li W, Li L, et al. Low-rank and sparse decomposition on contrast map for

small infrared target detection[C]//2018 24th International Conference on Pattern

Recognition (ICPR). 2018: 2682–2687.

[51] Huang Z, Zhao E, Zheng W, et al. Infrared small target detection via two-stage feature

complementary improved tensor low-rank sparse decomposition[J]. IEEE Journal of

Selected Topics in Applied Earth Observations and Remote Sensing, 2024, 17:

17690–17709.

[52] Zhao B, Wang C, Fu Q, et al. A novel pattern for infrared small target detection with

generative adversarial network[J]. IEEE Transactions on Geoscience and Remote Sens￾ing, 2021, 59(5): 5881–5892.

[53] Ma Z, Pang S, Hao F. Generative adversarial differential analysis for infrared small

target detection[J]. IEEE Journal of Selected Topics in Applied Earth Observations and

Remote Sensing, 2024, 17: 6616–6626.

[54] Ding H, Huang N, Wu Y, et al. Infrared small target detection improvement via hybrid

data augmentation using diffusion models and gan[J]. IEEE Transactions on Aerospace

and Electronic Systems, 2024: 1–16.

[55] Qi M, Liu L, Zhuang S, et al. Ftc-net: Fusion of transformer and cnn features for

infrared small target detection[J]. IEEE Journal of Selected Topics in Applied Earth

Observations and Remote Sensing, 2022, 15: 8613–8623.

[56] Shi S, Song Y. An improved yolov3 infrared small target detection algorithm[M]//2024

5th International Conference on Computer Vision, Image and Deep Learning (CVIDL).

2024: 1356–1359.

[57] Liu B, Jiang Q, Wang P, et al. Irmsd-yolo: Multiscale dilated network with inverted

residuals for infrared small target detection[J]. IEEE Sensors Journal, 2025: 1–1.

[58] Gupta M, Chan J, Krouss M, et al. Infrared small target detection enhancement using a

lightweight convolutional neural network[J]. IEEE Geoscience and Remote Sensing

Letters, 2022, 19: 1–5.

[59] Ma T, Yang Z, Liu B, et al. A lightweight infrared small target detection network based

on target multiscale context[J]. IEEE Geoscience and Remote Sensing Letters, 2023, 20:

1–5.

56

[60] Wu Y, Ding H, Liu Y, et al. Synthetic data augmentation for infrared small target detec￾tion via exploring frequency components and targets prior[C]//2024 IEEE International

Conference on Multimedia and Expo (ICME). 2024: 1–6.

[61] Serra, J. . "Image Analysis and Mathematical Morphology." Biometrics 39.2(1982):536.

[62] Rivest, Jean Franois , P. Soille , and S. Beucher . "Morphological gradients."

1992:326-336.

附件 1：团队介绍

介绍参赛队伍及合作队伍的各队员情况，若为研究生或博士，请补充介绍教研

室基本信息、研究方向、课题牵头人等相关情况。

57

