第十九届“挑战杯”中国青年科技创新“揭榜挂帅”擂台赛
 
基于轻量化国产大模型的高帧频弱小目标检测识别技术研究
幽瞳逐影：国产大模型轻量化实时红外弱小目标检测识别系统技术方案总结报告
摘要
简要介绍项目背景和核心挑战。
概述本方案采用的核心技术路线。
总结方案的关键创新点。
量化呈现核心成果（例如：模型参数量、在给定测试集上的召回率、虚警率、
时空稳定性等指标）。
针对国防安全领域高帧频红外序列中，弱小目标因信噪比低、特征缺失而难以检
测，且背景复杂多变导致虚警率高，以及高性能视觉大模型与装备端侧平台“轻量化、
低功耗、高实时性”要求存在范式冲突的核心挑战，本项目提出一种“数据预处理赋能
轻量化大模型”的创新技术路线。
该路线的核心是：首先，构建一套“先诊断，后处理”的场景自适应数据预处理流
水线，通过轻量化分类器智能识别“小目标”、“大/多目标”等不同场景，并调用最优的
背景抑制与目标增强策略，从数据源头提升信噪比，为后端模型“减负”；其次，围绕
国产大模型进行精细化微调与多维度（如量化、剪枝等）轻量化改造，使其在处理前
端输送的高质量数据时，能以更低的算力开销实现对目标时空特征的精准捕捉。
本方案的关键创新点在于，通过智能化的数据工程显著降低了对模型自身规模的
依赖，有效解决了大模型高性能与端侧部署约束之间的矛盾。最终，本方案旨在实现
召回率>0.90、虚警率<0.05、时空序列稳定性>0.90 等先进性指标，并成功将算法高效
部署于国产化昇腾硬件平台，验证该技术路线在兼顾高精度与高效能方面的巨大潜力
与实用价值，为提升我国国防装备智能化水平提供一套自主可控的解决方案。
2
目录
1 项目背景...................................................................................................................................... 4
1.1 项目研究背景....................................................................................................................4
1.2 核心技术挑战....................................................................................................................5
1.3 研究目标与意义............................................................................................................... 6
2 国内外研究现状.......................................................................................................................... 8
2.1 国防武器装备....................................................................................................................8
2.1.1 国内武器装备研究现状.........................................................................................8
2.1.2 国外武器装备研究现状...................................................................................... 10
2.2 红外弱小目标检测......................................................................................................... 11
2.2.1 基于传统方式的红外弱小目标检测................................................................. 11
2.2.2 基于深度学习的红外弱小目标检测................................................................. 14
2.3 模型端侧部署................................................................................................................. 17
2.3.1 模型压缩与优化技术.......................................................................................... 17
2.3.2 端侧推理框架....................................................................................................... 20
2.4 本章小结..........................................................................................................................22
3 算法设计方案............................................................................................................................24
3.1 总体技术路线................................................................................................................. 24
3.2 轻量化场景分类器......................................................................................................... 26
3.3 场景自适应数据预处理.................................................................................................28
3
3.3.1 小目标场景........................................................................................................... 29
3.3.2 大目标场景........................................................................................................... 30
3.3.3 多目标场景........................................................................................................... 31
3.4 轻量化国产大模型检测识别........................................................................................ 32
3.4.1 时序数据处理....................................................................................................... 33
3.4.2 检测大模型微调架构设计.................................................................................. 33
3.4.3 模型轻量化与泛化性增强.................................................................................. 34
3.4.4 小目标与多目标微调方案：跳帧策略微调.................................................... 37
3.4.5 大目标微调方案：时间窗口策略微调............................................................ 38
3.4.6................................................................................................................................ 39
3.4.7 小目标与多目标微调方案：跳帧策略............................................................. 39
3.4.8 大目标微调方案：时间窗口策略......................................................................40
3.4.9 检测与指标计算策略.......................................................................................... 41
4 算法部署方案............................................................................................................................44
4.1 目标部署平台与方案.....................................................................................................44
4.2 模型转化与优化............................................................................................................. 44
4.3 部署环境配置................................................................................................................. 44
4.4 部署验证与性能............................................................................................................. 45
5 验证与实验................................................................................................................................ 47
5.1 实验设置..........................................................................................................................47
5.2 实验结果与分析............................................................................................................. 47
6 总结与展望................................................................................................................................ 48
4
6.1 工作总结..........................................................................................................................48
6.2 未来展望..........................................................................................................................48
参考文献....................................................................................................................................... 49
5
1 项目背景
介绍项目研究背景、核心技术挑战、研究目标与意义等。
围绕国防武器装备方面展开相关论述。
1.1 项目研究背景
在现代多域联合作战与国家安全战略体系中，能否掌握全天候、全疆域的信息优势，
已成为决定胜负的关键。对高空、远距离、高机动性目标的早期预警与精确识别能力，
是构成这一信息优势的核心支柱。红外搜索与跟踪（IRST）系统，凭借其被动探测的
隐蔽性、强大的抗电磁干扰能力以及穿透烟尘雾霾的特性，成为现代侦察预警、精确
打击和武器制导系统中不可或缺的“火眼金睛”。
然而，在应对高帧频红外图像序列中的“弱小目标”时，现有技术正面临前所未有
的挑战。这些目标，如蜂群无人机、隐身飞行器、临近空间高超音速导弹等新质作战
力量，在远距离成像时，信号特征极其微弱，通常仅在焦平面上占据数个像素，缺乏
可供辨识的稳定形状、轮廓与纹理信息。其微弱的红外辐射信号极易淹没在复杂的云
层、崎岖的地物、波动的海面乃至城市热岛效应形成的背景杂波之中，信噪比极低，
使得传统的检测技术遭遇瓶颈。
传统基于模型驱动的检测算法，例如空域滤波、形态学处理等，虽计算开销小、
易于实现，但其设计哲学根植于对目标和背景的先验假设（如目标比邻域更亮、背景
连续平滑等）。当面对动态多变、非线性的复杂战场环境时，这些固化的假设极易失
效，导致算法泛化能力严重不足，虚警率和漏检率难以满足实战需求。近年来，以深
度学习，特别是视觉大模型为代表的数据驱动方法，凭借其从海量数据中自主学习深
层抽象特征和复杂上下文关联的强大能力，为突破这一瓶颈带来了曙光。理论上，大
模型能够更深刻地理解目标的时空演化规律与背景的动态干扰模式，从而实现更鲁棒
的区分。
尽管如此，将通用大模型直接“移植”到国防装备端侧平台，又会引发新的、更为
尖锐的矛盾。机载、弹载、星载等尖端武器平台对算法的实时性（毫秒级响应）、低
功耗（瓦特级能耗）和轻量化（兆字节存储） 有着极为严苛的物理约束，而大模型动
辄数十亿的参数量和海量的计算需求与其存在着天然且深刻的冲突。因此，本项目研
究的核心，不再是单纯追求更大、更深的网络模型，而是旨在探索一条全新的、更具
6
效费比的技术路径：如何通过智能化的前端数据处理，主动提升数据质量，从而“赋能”
一个轻量化的国产大模型，使其在满足严苛部署约束的同时，实现对高帧频红外弱小
目标的高性能检测与识别。 这代表着一种从“依赖模型蛮力”到“依靠智能策略”的设计
哲学转变。
1.2 核心技术挑战
尽管战略价值巨大，但在资源受限的国产化平台上实现高帧频、高精度的红外弱
小目标检测，技术层面面临着相互交织的四大核心挑战：
1. 目标信号极端微弱，有效特征严重缺失：目标信号在经过大气传输路径时，会
遭受吸收、散射和湍流效应的严重衰减，抵达传感器时已是强弩之末。其在焦
平面上成像小、能量弱，信号强度常与背景的自然起伏和探测器自身的读出噪
声处于同一量级。如何在极低的信噪比下，从像素级的噪声海洋中“打捞”出稳
定、可供识别的有效特征，是首要且最根本的难题。
2. 背景动态复杂，强相关干扰源众多：红外图像的背景远非平坦均匀。天空背景
中，云层的边缘和絮状结构会形成大量高亮度的伪目标；地面背景中，地物/建
筑轮廓、植被反射差异以及人为热源都会产生强烈的边缘和杂波；海空背景下
的波浪反光（海天杂波）更是公认的检测难题。这些背景杂波在形态、尺寸和
亮度上与真实弱小目标高度相似，是造成虚警的根本原因，对算法的辨识能力
构成了严峻考验。
3. “大模型”与“轻量化”的范式冲突：这是一对根植于物理定律的矛盾。一方面，
任务的极端复杂性（低信噪比、高杂波）呼唤大模型强大的上下文理解和特征
辨识能力。另一方面，而端侧平台（如高速飞行的导弹、长时续航的无人机）
的算力、功耗和存储空间是极其宝贵的稀缺资源。如何在“追求极致性能”与“适
应苛刻现实”这对矛盾体之间取得精妙的平衡，是当前学术界与工业界共同面临
的焦点与难点。
4. 场景多样性带来的适应性难题：战场环境瞬息万变，目标类型、背景形态、干
扰强度、乃至气象条件都在不断变化。一个在晴朗天空背景下表现优异的算法，
可能在复杂地面或云层背景下性能骤降。若采用“一刀切”的单一固定处理流程，
难以做到“对症下药”，甚至可能在抑制背景的同时过度削弱目标信号，或在增
7
强目标时引入更多伪影。因此，算法必须具备高度的场景自适应能力，能够智
能识别当前场景并动态调整自身策略。
1.3 研究目标与意义
为应对上述挑战，本研究的核心研究目标是：构建一套国产大模型轻量化实时红
外弱小目标检测识别系统基于轻量化国产大模型的、具备场景自适应能力的、端到端
的高帧频红外弱小目标检测识别算法与部署方案。 具体而言，我们旨在：
1. 技术路线上，重点突破“先诊断，后处理”的自适应数据预处理技术。该路线的
核心是：在将数据“喂给”大模型之前，先通过一个高效的场景分类器对图像进
行智能分析，判断其属于何种场景（如小目标、大目标、复杂多目标等），随
后调用与之匹配的最优增强与背景抑制策略。这种主动的数据“净化”和“增强”，
能极大提升信噪比，从根本上降低后端大模型的检测压力和对模型规模的依赖。
2. 模型设计上，围绕国产大模型进行精细化的轻量化设计与高效微调。我们并非
从零构建，而是站在巨人的肩膀上，利用国产大模型的强大基础能力，通过知
识蒸馏、结构化剪枝、量化感知训练等多种手段，在保留其核心识别能力的前
提下，将其改造为适用于端侧部署的“精锐部队”，使其能够高效利用前端处理
后的高质量数据，专注于时空特征的深度挖掘，实现精准检测。
3. 性能指标上，在给定的测试集上，冲击召回率>0.90、虚警率<0.05 及序列稳定
性> 0. 90 的先进性指标。 同时， 确保整套算法在国产化硬件平台（ 华为昇腾）
上的高效、实时运行，完成从算法设计到工程实践的全链路验证。
本研究的意义不仅在于为我国国防装备提供一种解决“卡脖子”难题的高性能解决
方案，更体现在以下两个层面：
1. 推动技术范式革新：本项目探索的“数据预处理赋能大模型”技术路线，为解决
一切资源受限平台下的复杂 AI 任务（如移动医疗影像分析、工业流水线异常检
测等）提供了新的解题思路。它验证了通过前端智能化数据工程，可以有效降
低对后端模型规模和算力的过度依赖，为未来构建更普适、更经济、更高效的
AI 系统提供重要的范例和理论支撑。
8
2. 夯实国防技术自主可控：在当前日益复杂的国际形势下，核心技术的自主可控
是国家安全的基石。本研究从算法的理论基础（国产大模型）到最终的硬件载
体（国产 AI 芯片），全面依托自主可控的技术体系，旨在打通一条不受外部制
约的“设计-实现-部署”全链路。这对于提升我国国防装备的信息化、智能化水平，
突破技术封锁，在激烈的国际军事技术竞争中构筑坚实的国家安全科技屏障，
具有深远且紧迫的战略意义。
9
10
2.1.1
2.1
2
（1）导弹预警与精确制导系统
在导弹预警领域， 长春光机所基于 YOLOv4 框架开发的空间红外弱目标检测技术
，通过增加 104×104 特征尺度和 K-means 聚类优化先验框尺寸，显著提升了复杂云 [ 18]
层背景下的目标检出率， 检测速度达 38. 99ms/ 帧， 大幅降低云层干扰下的漏检率。该
技术已应用于我国新一代天基预警系统，实现对高超音速导弹的早期探测。火箭军工
程大学则专注于弹载平台的轻量化实时检测，提出 YOLO-IDSTD 模型[ 21]，通过 Focus
模块减少推理耗时，采用四尺度检测和路径聚合网络强化特征融合，使模型尺寸压缩
至 7.27MB，满足弹载嵌入式系统对实时性和低功耗的严苛要求。西北工业大学的先检
国内外研究现状
测后跟踪技术[ 22] 采用增强型简化高通滤波预处理， 结合自适应阈值分割， 在低信噪比
条件下仍能保持高检测概率，已应用于空空导弹的红外成像制导系统。
近年来，随着传感器性能提升与计算机视觉技术的快速发展，基于高帧频红外图
像序列的弱小目标检测与识别技术在军事侦察、航空航天、灾害监测等领域得到了广
泛研究与应用。红外弱小目标往往是潜在威胁或关键目标的早期表现，其在早期预警
与态势感知中具有不可替代的作用。国内外学术界和工业界围绕目标特征提取、背景
抑制、检测识别算法优化等方面开展了大量研究，形成了从传统图像处理方法到深度
学习、大模型驱动算法的多种技术路线。然而，现有方法在低信噪比下的稳定性、复
杂背景干扰的抑制能力、以及轻量化与高精度的平衡方面仍存在不足。本章将系统地
梳理高帧频红外弱小目标检测与识别的研究现状，分析各类方法的优缺点与发展趋势，
为后续基于轻量化国产大模型的技术方案提供支撑。
国防武器装备
红外弱小目标检测技术作为现代国防武器装备的核心能力之一，直接关系到导弹
预警、反无人机作战、隐身目标识别等关键军事能力的效能，目前已成为各国国防武
器竞争的战略高地。
国内武器装备研究现状
我国在红外弱小目标检测领域的武器化应用研究在导弹预警、反无人机、舰载防
御等领域取得显著突破。表 2-1 总结了部分国内代表性红外弱小目标检测武器系统。
11
（2）反无人机与集群目标识别
针对“低慢小”无人机的防御需求，基于密度-距离空间的红外检测方法[ 20]通过局部
灰度峰值特性构建二维空间模型，实现多目标自适应检测，平均每帧处理时间仅
0.0212 秒，在连续杂波背景下 AUC 值接近 1，且不受目标尺寸形状变化影响，已集成
于野战防空系统的反无人机模块。针对集群目标识别难题，武汉大学提出改进的
IDBSCAN-DM 算法[25]，通过多尺度滑动窗口快速提取候选目标，融合显著性和分布特
征，有效解决了无人机群、多弹头分导等集群目标的特征提取难题，显著提升了复杂
战场环境下对饱和攻击的应对能力，检测概率较传统方法提高 30%以上。
（3）舰载防御与激光反制系统
在舰载防御领域，西安电子科技大学开发的逐像素注意力网络[ 19]采用 U 形注意力
块和稠密融合技术，通过增强小目标的空间与通道特征，有效抑制海杂波干扰，在
NUDT-SIRST 数据集检测率达 98. 84%，虚警率仅 2. 92×10-6，已应用于舰载红外搜索
与跟踪系统。 西安知语云公司集成的激光反制系统[ 23] 则融合量子阱红外探测器、 雷达
与频谱监测模块，可识别-40℃下 0. 1℃温差，探测精度达 98. 7%，实现与空管雷达联动，
成功完成对巡航导弹靶机的拦截验证，该系统代表了国内在多模态传感集成领域的最
高水平，已在多个重点军事设施部署应用。
表 2-1：国内代表性红外弱小目标检测武器系统
天基预警系统
弹载制导模块
野战防空系统
舰载 IRST 系统
反无人机系统
武器
多尺度 YOLOv4 增强
YOLO-IDSTD 轻量化
激光-雷达-红外融合
逐像素注意力网络
密度-距离空间模型
技术
38.99ms/帧，云层漏检率降低
40%
CPU 推理速度提升 36.1%，模
型 7.27MB
探测精度 98.7%，温差分辨率
0.1℃
Pd=98.84%，Fa=2.92×10-6
0.0212s/帧，AUC≈1
指标
高超音速导弹预警
空空/地空导弹导引
头
巡航导弹拦截
舰艇防空反导
“低慢小”无人机拦截
应用
12
J-20 等隐身战机，计划于 2025 年底开展对抗性气候测试，目标是与 Akash-NG 防空网
络协同，挑战现有隐身技术优势。伊朗锡斯坦大学提出的抗噪声检测算法通过噪声像
素抑制提升检测鲁棒性， 在 431 幅含强噪图像中虚警率显著低于传统方法。 该技术特
别适用于中东地区的沙尘暴环境，已集成于伊朗“信仰”-373 防空系统。
俄罗斯则重点发展红外-雷达复合探测技术，其 S-500 防空系统[ 24]采用双波段红外
探测与有源相控阵雷达协同工作机制，通过时空特征融合张量模型增强弱小目标检测
能力，在乌克兰战场实际部署中展现出对低空巡航导弹的优异拦截能力。
表 2-2：国外代表性红外弱小目标检测武器系统
2.1.2 国外武器装备研究现状
全球军事强国正加速推进高帧频红外弱小目标检测技术在武器平台的集成应用，
形成以全景感知、 电子对抗和隐身探测为核心的发展路线。 表 2-2 总结了部分国外代
表性红外弱小目标检测武器系统。
欧美国家在舰载防御系统和电子对抗领域保持技术领先。法国泰雷斯公司升级的
Murin 雷达采用 X 波段电子扫描阵列，支持超 50 个目标同步跟踪，探测距离达 20 公
里(车辆)，8 公里(人员)，新增跳频和低截获概率波形技术，显著增强抗电磁干扰能力，
适用于连级机动部署。美国海军研发的 SPEIR 舰载光电系统[ 23]在 Block 1 阶段已实现
360°被动光学探测与激光测距，Block 2 阶段将整合 AI 驱动的 LockNESS 算法，可自
动生成威胁全景图并引导软杀伤系统，该系统代表了舰载光电防御的最高水平，其多
目标实时跟踪能力对反舰导弹饱和攻击具有显著抑制效果。
在反无人机领域，美国 Skylark Labs 开发的 ARIES 系统[23]采用光学传感器与 AI 融
合方案， 突破传统射频监测局限， 实现对无射频信号“ 暗无人机” 的探测， 在 Camp
Atterbury 演习中成功识别多型未知构型无人机， 填补非辐射目标探测空白。 美国海军
研究实验室开发的神经时空张量模型[ 24] 结合低秩张量分解与三维全变分约束， 在动态
背景中实现 17fps 实时检测，模型参数量减少至传统方法的 1/16.6，适用于边缘计算设
备，已部署于单兵便携式反无人机系统。
印度和俄罗斯在隐身目标探测和抗干扰算法领域取得突破性进展。印度国防研究
与发展组织 DRDO 开发的光子雷达利用光波替代无线电波进行探测，宣称可识别 F-35、
国家 武器 技术 指标 应用
13
2.2.1
2.2
鲁棒性差[43]。基于滤波的方法主要分为以下两种实现方法。
第一种是基于空域滤波算法，主要利用目标与背景在空间分布上的统计差异，通
过分析邻域像素间的空间关联特性来估计背景灰度分布，进而通过差分运算实现目标
的增强显示。Marvasti 等人[32]在 2018 年的研究中，针对传统 Top-Hat 变换在目标识别
方面的不足，提出了改进的 Top-Hat 变换算法，增强了对真实目标的识别能力。Wang
等人[ 33] 通过设计多向结构单元形态变换融合机制， 在充分挖掘目标与背景对比度信息
的基础上，有效降低了复杂背景条件下的虚警率。
第二种是基于频域滤波算法，专注于目标、背景和噪声在频率域的本征差异，通
过在频域空间构建专门的滤波器来实现背景与噪声的有效抑制。为解决高维特征空间
的适用性问题，Anju 和 Raj 等人[ 34]系统性地提出了包括剪切波变换在内的多尺度变换
红外弱小目标检测
现阶段，红外弱小目标检测手段主要分为两大类：其一为基于传统方式的红外弱
小目标检测，包括基于滤波的方法、基于人类视觉系统的方法和基于图像数据结构的
方法；其二是基于深度学习的红外弱小目标检测[29]。
基于传统方式的红外弱小目标检测
（1）基于滤波的方法
小目标检测最早采用基于滤波方式，根据分析噪声、背景及目标在频域上的频率
差异设计出能够过滤背景噪声的滤波器，从而实现检测目标。这种检测方法实现简单，
但仅仅通过频率差异进行区分出背景噪声和目标，无法应对复杂背景下的检测问题，
法国
美国
印度
伊朗
俄罗斯
Murin 雷达升级
版
SPEIR 舰载系统
ARIES 系统
光子雷达
BLCM 抗噪算法
红外-雷达复合
探测
X 波段 ESA 扫描+LPI
波形
360°被动光学
+LockNESS AI
光学-AI 融合
光波替代无线电波
分支局部对比度测度
时空特征融合张量模型
超 50 目标同步跟
踪，20km 车辆探测
多目标实时跟踪/分类
无射频信号目标检测
高精度隐身目标识别
强噪环境下低虚警率
低空巡航导弹拦截能
力优异
连级机动部署
2025 年作战测试
Camp Atterbury
验证
2025 年气候测试
集成于“信仰”-373
防空系统
集成于 S-500 防
空系统
方法族。Wang 等人[35]提出了基于非负约束变分模态分解的检测框架，该方法的显著优
势在于无需预设参数配置，即使面对窄频带信号也能实现有效的成分分离。
（2）基于人类视觉的方法
基于人类视觉的方法的核心在于对人类视觉系统显著性感知过程的计算建模，通
过量化目标在局部邻域内的显著性特征来实现检测功能。这类方法的理论基础建立在
目标与背景在空间、灰度、纹理等多个维度上的不连续性假设之上，通过构建局部对
比度或显著性映射的计算框架来突出目标区域[26]。
Chen 等人[ 36]提出局部对比度检测理论，通过量化目标在空间分布上不连续特性[ 37]，
建立基于 3×3 邻域灰度差异的像素显著性评估机制。 尽管该方法在计算效率上表现优
异， 但在复杂背景环境下抗噪声能力仍需改进。 Han 等人[ 38] 通过重新设计邻域计算策
略，显著减少冗余的像素比较操作，在保持检测精度的前提下大幅提升算法执行效率。
针对不同类型目标的特性差异， Wei 等人[ 39] 提出了多尺度块图像对比度分析方法。
采用分块处理策略，通过在各个图像块内独立计算对比度，最终通过多尺度融合机制
生成综合显著性映射，从而在复杂背景条件下展现出更强的目标检测能力。Bai 等人[ 40]
提出了基于导数特征的对比度测试框架，通过计算图像的多阶导数信息，在各个导数
子带上构建对比度映射，最终通过加权融合策略生成最终的显著性图像，有效捕获了
目标的边缘和纹理特征。
针对高亮度背景抑制问题， Han 等人[ 41] 设计了基于匹配滤波器和最近均值原理的
背景估计模型，通过精确估计局部背景均值并利用匹配滤波器增强目标信号，实现了
目标与背景的有效分离。Lu 等人[ 42]提出的多方向导数加权对比度测量方法，通过深度
挖掘图像导数特性并结合创新的局部对比度测量机制，进一步提升检测算法的鲁棒性。
（3）基于图像数据结构的方法
基于图像数据结构的方法是红外弱小目标检测中的另一类重要方法，通过深入分
析图像数据的内在结构特性——包括稀疏性、低秩性、张量结构等，实现目标与背景
的有效分离。这类方法优势在于能够充分利用目标和背景在数据结构层面的本质差异，
通过构建相应的数学优化模型并设计高效的求解算法来实现精确的目标检测[29]。
Zhao 等人[ 44]提出一种基于稀疏表示的目标检测方法，通过构建过完备字典将图像
分解为稀疏目标分量和低秩背景分量，有效提升目标与背景的分离效果。在此基础上，
Zhao 等人[ 45]提出一种结合稀疏性和边缘保留平滑的结构协同稀疏性检测算法，结合了
稀疏性和边缘保留平滑技术。通过在梯度零范数基础上提取大梯度分量来改善背景建
14
模，同时利用行范数对背景稀疏性进行建模，并通过误差矩阵的列范数实现红外弱小
目标的精确定位，从而显著增强了算法的鲁棒性表现。
针对复杂背景下的目标检测问题，Liu 等人[46]提出一种通过联合低秩和局部平滑度
先验进行红外弱小目标检测方法，通过滑动三维窗口构建改进的时空模型，利用张量
相关总变分表征背景，并采用范数约束消除强残差，最后设计高效的交替方向乘子法
求解模型。Yin 等人[ 47]提出了一种新的 3-D 范式框架，将时空加权和正则化结合在低
秩稀疏张量分解模型中，通过设计时空局部先验结构张量、引入三向对数的张量核范
数和采用加权三向总变分正则化来区分目标与背景并约束平滑度，同时开发基于乘子
交替方向方法的高效求解方法及快速张量方程加速子问题求解。
针对动态背景适应、 转置误差及稀疏性近似等关键问题， Sun 等人[ 48] 提出自适应
时空红外张量和加权张量平均秩近似的解决方案，通过交替方向乘子法求解，有效解
决低秩稀疏分解方法在动态背景适应方面的固定时间步长限制、低秩张量恢复过程中
的转置误差敏感性以及稀疏度次优 L1 范数近似导致的物理表示不准确等关键技术难题。
在稀疏性与低秩性融合研究方向， Wei 等人[ 49] 提出基于时间低秩和稀疏表示的鲁
棒检测方法，通过充分利用目标补丁的时间性和低秩性质，将目标检测问题转化为低
秩稀疏学习问题，并通过时间字符组合策略缩小检测区域，减少背景干扰并提升检测
的准确性和效率。Deng 等人[ 50]提出局部和非局部先验融合检测方法，通过引入基于滑
动双窗口的创新局部对比度测量机制，并与低秩稀疏分解技术相结合，增强目标和背
景之间的分离能力， 特别是在抑制背景中 PNHB 和高强度结构方面表现突出。 Huang
等人[ 51] 提出两阶段特征互补改进张量低秩稀疏分解方法， 利用本地先验信息初始化
TLRSD 模型，有效增强本地和非本地特征之间互补性，实现更加精确的目标定位效果。
2.2.2 基于深度学习的红外弱小目标检测
传统的红外弱小目标检测方法很大程度上依赖于人工设计的特征提取和先验知识，
通过固定的滤波和建模流程来获取目标特征，这种固化的处理模式限制了其在不同应
用场景下的适应能力和优化空间。相比之下，深度学习运用端对端的学习模式，神经
网络能够自主从数据当中挖掘目标的深层次特征，从而实现检测性能的显著提升。
生成对抗网络在红外弱小目标检测领域的应用展现出独特的优势。Zhao 等人[ 52]提
出基于生成对抗网络的检测框架，通过生成器对复杂背景的精确建模和判别器对目标
区域的专门识别，显著降低误警率。Ma 等人[53]在此基础上进一步发展了生成对抗差分
15
分析模型，通过快速行进法构建伪背景标签，利用背景引导的生成对抗网络学习背景
数据的潜在分布特性，再通过差分分析获得包含小目标感兴趣区域的差分图像，最终
对感兴趣区域进行精细化表征以获得检测结果。这种方法的优势在于充分考虑了图像
背景对目标检测的影响，从而提高检测的准确率。Ding 等人[ 54]针对训练数据不足这一
制约卷积神经网络性能的关键问题，提出两阶段红外小目标图像增广策略，通过背景
填充和扩散模型生成高质量且多样化的复杂背景图像，并利用基于生成对抗网络的目
标自适应融合技术实现目标与背景的无缝整合，生成高度逼真的红外小目标增强图像。
针对红外弱小目标尺寸微小、特征表达不明显问题，Qi 等人[55]提出 Transformer 和
CNN 融合的检测架构，该架构采用双分支设计：CNN 分支通过具有跳跃连接的 U-Net
结构获取小目标的低级局部特征，而 Transformer 分支则利用分层自注意机制学习长程
上下文依赖关系，从而抑制背景干扰并增强目标特征表达。此外，设计特征融合模块
实现了两个分支特征的有效整合，进而实现更加精确的目标检测。
YOLO 系列模型在红外弱小目标检测领域展现出卓越的性能表现。Shi 等人[56]提出
改进 YOLOv3 算法，通过将特征提取阶段的 32 倍下采样检测头替换为 4 倍下采样检测
头来适应小目标的稀疏分布特性，并在特征融合阶段引入红外 IAM 聚焦机制实现感兴
趣区域的特征聚焦，从而显著提升了检测性能。Liu 等人[57]设计倒置残差多尺度膨胀网
络，通过倒置残差特征提取模块实现红外小目标特征的有效提取，借助频率加权特征
融合技术平衡目标定位与噪声抑制之间的关系，并利用倒置残差多尺度扩张注意力模
块降低背景噪声的影响，增强了对弱小目标的感知能力。
考虑到实际应用中对实时性的严格要求，Gupta 等人[58]提出轻量级目标检测网络架
构，通过模型压缩和计算加速策略，在维持检测精度的前提下显著降低了计算复杂度。
Ma 等人[59]为了减少模型参数规模，提出多尺度目标上下文特征提取模块，能够丰富目
标的特征表达能力；并设计融合深浅层特征的特征映射上采样网络架构，提升轻量化
红外弱小目标检测网络的特征映射能力。
在数据增强和模型训练优化方面，Wu 等人[60]提出结合空间低频 StyleGAN 背景生
成网络和检测难度驱动的 Transformer 目标融合模型的两阶段数据增强策略，通过改进
红外背景生成质量和自适应目标强度融合技术，有效缓解红外小目标检测中训练数据
不足的问题。
图 2-1 展示了红外弱小目标检测研究方法时间轴。 综合分析上述各类检测技术的
发展现状可以发现，传统的基于滤波、人类视觉和图像数据结构的方法主要通过对图
16
像中目标和背景灰度特性的深入分析，实现背景抑制和目标-背景对比度增强的目
标。尽管这些方法在特定场景下表现良好，但其较高的计算复杂度和有限的场景适应
能力制约了实际应用效果。相比之下，基于深度学习的检测技术通过引入先进的特征
提取网络架构，能够挖掘目标更深层次的特征信息，相较于传统方法在复杂应用场景
下展现出更强的适应能力和鲁棒性表现。然而，现有深度学习算法在检测精度和误警
率控制方面仍存在进一步优化的空间。
图 2-1 红外弱小目标检测研究方法时间轴
2.3 模型端侧部署
在高帧频红外弱小目标检测的实战环境中，机载、弹载、星载等平台往往需在算
力、功耗与存储受限条件下，实现毫秒级响应和高精度识别。然而，依赖云端计算不
仅会引入通信延迟，还会在战时面临链路受限、干扰甚至中断的风险；同时，敏感红
外成像数据外传存在安全隐患，与国防装备的高安全性要求相悖。因此，将检测模型
直接部署在任务平台本地，成为兼顾实时性与安全性的必然选择。为了实现端侧部署，
17
18
练成本更高。
（2）剪枝技术
剪枝旨在移除冗余参数以降低模型复杂性，从而提升推理速度与部署效率。Hu 等
人 提出的神经元剪枝方法基于数据驱动分析，裁剪网络中贡献较小的神经元，从而减 [3]
少计算量。Molchanov 等人 设计了卷积核剪枝策略，通过评估卷积核的重要性来移除 [ 4]
低贡献卷积核，显著降低了计算开销。He 等人 进一步提出通道剪枝方法，在保证精 [ 5]
度的前提下减少通道数量以适配端侧硬件，这类结构化剪枝方法与并行计算架构高度
兼容，但压缩率相对有限。相比之下，Guo 等人 的动态剪枝方法在推理过程中可根据 [6]
Liu Liu 输入特征动态移除权重， 等人 利用频域信息进行动态剪枝以提升压缩率，而 [ 7]
研究者们主要从两大方向展开优化：一是通过模型压缩与优化技术减少参数规模与计
等人 提出的自动结构化剪枝框架则通过搜索最优剪枝策略实现超高压缩率。尽管非结 [ 8]
算量，从源头降低硬件负担；二是借助高效的端侧推理框架充分发挥硬件特性，实现
推理加速与跨平台适配。
2.3.1 模型压缩与优化技术
在深度学习模型端侧部署的过程中，如何在有限的计算与存储资源下保持较高的
推理精度与速度，是学术界与工业界的核心挑战之一。模型压缩与优化技术作为解决
这一问题的重要途径，旨在通过减少参数规模与计算量，实现模型在边缘设备上的高
效运行。本节将系统介绍深度学习模型轻量化的主要技术路径，包括降低数值精度以
减少计算与存储的量化、移除冗余参数的剪枝、利用教师模型提升小模型性能的知识
蒸馏，以及通过矩阵分解减少计算开销的低秩分解。
（1）量化技术
量化通过降低模型权重与激活的数值精度来减少模型规模与计算开销，是在端侧
Frantar GPTQ 部署中提升运行效率的重要手段。 等人 提出了 方法，该方法在后训练 [ 1]
量化（PTQ）阶段引入高效的补偿策略，使得模型在权重量化至 3 或 4 位时仍能保持
Jacob QAT 较高的精度。 等人 则提出了量化感知训练（ ）方法，将量化过程直接融入 [ 2]
训练阶段，使模型在训练中逐步适应低精度约束，从而在量化后显著减小精度损失。
相较于 PTQ，QAT 在需要极低比特量化的任务中表现出更优的精度保持能力，但其训
19
从知识类型角度，KD 方法可分为三类：
①基于响应的蒸馏（Response-based KD）：直接对齐教师与学生的预测分布，方
Hinton KD Ba 法简单高效。 等人 提出的 框架通过软标签引导学生模型训练， 等人 [9] [10]
则提出将深层模型压缩为浅层模型以减少推理复杂度。
②基于特征的蒸馏（Feature-based KD）：通过对齐中间层特征表示或注意力图传
Romero FitNets 递结构性知识。 等人 提出的 利用教师模型的中间层特征指导学生网络 [11]
Zagoruyko Attention Transfer 训练，以提升收敛速度与精度； 等人 提出的 方法则通过 [12]
蒸馏空间注意力分布有效提升检测与识别性能。
③基于关系的蒸馏（Relation-based KD）：关注样本间或特征间的相互关系。Park
构化剪枝能够获得更高的参数压缩比， 但其在硬件加速上的适配性较弱。 表 2-3 总结
RKD Tian 等人 提出的 方法通过匹配样本对之间的距离与角度信息传递高阶关系； 等 [13]
了常见的模型剪枝方法
表 2-3：常见模型剪枝方法对比
（3）知识蒸馏技术
知识蒸馏（Knowledge Distillation, KD）是一种通过将大型、高性能教师模型的知
识迁移至小型学生模型，从而在显著减少参数规模与计算开销的同时，尽可能保持甚
至提升模型性能的技术。根据学生模型是否能访问教师模型的内部信息，KD 可分为黑
Hinton Soft Targets 盒蒸馏与白盒蒸馏两类。 等人 提出的软标签蒸馏（ ）方法是典型的 [9]
黑盒蒸馏，通过在输出概率分布中引入温度参数平滑类别分布，从而捕获类间相似性
信息。白盒蒸馏则允许学生模型访问教师模型的中间特征或结构信息，实现更细粒度
的知识迁移。表 2-2 总结了常见的知识蒸馏方法。
类别
结构化剪枝
非结构化剪枝
核心思想
移除参数子集（如神
经元、卷积核、通
道），保持网络结构
规则性
以细粒度移除单个权
重参数
代表方法
神经元剪枝[3]、
卷积核剪枝[4]和
通道剪枝[5]
动态剪枝[6]、迭
代剪枝[7]和自动
结构化剪枝[8]
优点
与硬件架构高度
兼容，可充分利
用并行计算；部
署友好
压缩率高，显著
减少存储需求
缺点
压缩率相对较
低，精度下降
风险较高
硬件并行计算
支持差，推理
加速效果有限
20
速推理并压缩存储。在深度神经网络中，LRF 常用于全连接层与卷积核的加速，其中
卷积核的分解可借助张量分解方法（如 CP、Tucker、Tensor-Train 分解）降低卷积运
FLOPs Sainath SVD 算的 。 等人 率先在语音识别任务中将奇异值分解（ ）应用于全连 [15]
接层，实现了推理速度的提升；Jaderberg 等人 提出将卷积核分解为一维卷积组合， [16]
在保持精度的同时显著减少计算量。
随着模型规模扩大，LRF 逐渐与量化、稀疏化等技术结合形成混合压缩策略。例
Yao LRF PTQ Low-Rank 如， 等人 将 与后训练量化（ ）结合，提出低秩补偿（ [17]
Compensation, LoRC）方法，通过引入补偿矩阵弥补低秩近似带来的精度损失，有效平
衡压缩率与准确率。近期研究还提出自适应低秩分解方法，可根据输入特征动态调整
CRD 人 提出的 框架结合对比学习保持特征空间结构一致性，从而提升学生模型的泛 [14]
分解秩，实现任务与样本级的精细化加速。在弱小目标识别等对精度与延迟均敏感的
场景中，LRF 能在保持检测性能的同时显著降低推理延迟，具有重要的应用潜力。
化能力。
黑盒蒸馏因部署简便而适合快速压缩，而白盒蒸馏，尤其是特征蒸馏与关系蒸
馏，能够在保留细粒度特征的同时有效提升小模型的检测精度，因此成为近年来的研
究热点。
表 2-4：常见知识蒸馏方法对比
（4）低秩分解技术
低秩分解（Low-Rank Factorization, LRF）通过将大型权重矩阵近似分解为低秩矩
阵乘积，利用权重在高维空间中的冗余性与低秩特性，在减少参数与计算量的同时加
类别
基于响应的蒸馏
基于特征的蒸馏
基于关系的蒸馏
核心思想
对齐教师与学生的输
出概率分布
对齐教师与学生的中
间层特征表示或注意
力图
保持样本间或特征间
的相对关系（距离、
角度、相似度）
代表方法
软标签蒸馏
、模型压缩 [9]
[10]
FitNets
[11]、
Attention
Transfer
[12]
RKD[13]、
CRD[14]
优点
实现简单，计算
开销小，适用广
泛
捕获结构性与空
间信息，对检测
等任务效果好
传递高阶结构知
识，提升泛化性
缺点
仅利用最终输
出信息，忽略
中间特征
特征对齐需额
外计算与显存
开销
关系构造与计
算复杂，需较
大 batch 支持
21
Delegate、NNAPI Delegate）、算子融合等技术，TensorFlow Lite 能够在低功耗、低内
存设备上实现高效推理，广泛应用于移动视觉、语音和嵌入式 AI 场景。
PyTorch Mobile 是 PyTorch 框架在移动端的延伸，现已与 PyTorch Lite Interpreter
深度整合，支持静态图与动态图模型在端侧的部署。其优势在于保持模型的动态执行
特性与灵活调试能力， 并可通过 TorchScript、 量化与算子裁剪实现推理加速， 适合需
要快速迭代和跨平台部署的应用场景。
（2）面向特定硬件平台优化的端侧推理框架
HiAI 是华为公司针对自研麒麟 SoC 和昇腾 NPU 架构深度优化的端侧推理框架，
表 2-5 总结了常见的低秩分解方法。
结合 CANN（Compute Architecture for Neural Networks）算子库与 MindSpore Lite，可
表 2-5：常见低秩分解方法对比
2.3.2 端侧推理框架
在模型端侧部署领域，端侧推理框架作为连接深度学习模型与终端设备的核心桥
梁，已成为优化推理性能与适配多硬件平台的重要研究方向。不同类型的端侧推理框
架在性能优化策略、硬件适配能力和生态支持方面各具特色。以下按照框架类型进行
分类介绍。
（1）基于通用深度学习框架的端侧推理框架
TensorFlow Lite 是谷歌公司专为移动端与嵌入式设备开发的轻量化推理引擎，继
承了 TensorFlow 的算子库与生态优势。通过量化（int8、fp16）、Delegate 加速（GPU
类别
基于矩阵分解的
低秩分解
基于张量分解的
低秩分解
自适应低秩分解
核心思想
将全连接层权重矩阵
分解为低秩矩阵乘积
对卷积核权重张量进
行多维分解
根据输入特征或任务
动态调整分解秩
代表方法
SVD 分解[15]
CP 分解[16]
LoRC
[17]
优点
实现简单，对全连
接层加速效果显著
能有效加速卷积运
算，适合 CNN 压缩
精度保持能力强，
适应多任务
缺点
对卷积结构适配
性差
分解与重构开销
较大，调参复杂
实现复杂，需额
外控制逻辑
充分利用华为 NPU 的算力优势，支持图算融合、内存复用和多数据流并行执行，大幅
提升推理效率与功耗比，广泛应用于华为终端设备的图像识别、目标检测等任务。
TensorRT 是英伟达面向 GPU 和 Jetson 系列的高性能推理框架，通过层融合、动
态张量内存管理、精度校准（int8/fp16）等技术，可显著提升模型在 GPU 端的吞吐量
与延迟表现，常用于自动驾驶、工业检测等高实时性场景。
此外，vLLM-Ascend 是针对华为昇腾 NPU 优化的开源推理框架，基于 vLLM 架
构并结合 CANN 与 torch-npu，实现了大模型在国产 NPU 上的高吞吐推理，支持张量
并行、流水并行以及 KV Cache 高效管理，非常适合在国产化 AI 芯片上部署轻量大模
型。
（3）专注于模型轻量化与高效的端侧推理框架
MNN（Mobile Neural Network）由阿里巴巴推出，面向移动端场景进行深度优化，
支持多种模型格式转换与自动算子调度，具备高度的模型压缩与加速能力，尤其适合
资源受限环境下的人脸识别、商品识别等任务。
Paddle Lite 是百度推出的跨平台推理框架，继承了 PaddlePaddle 的高性能内核，支
持 ARM CPU、RISC-V、GPU、FPGA、NPU 等多硬件平台，通过混合精度推理、算
子融合和子图优化，实现多场景下的快速推理。
在国产化轻量推理领域， ncnn（ 腾讯开源） 因其极低的依赖和高度的可移植性，
在移动端和嵌入式设备上广泛应用，特别适合小体积、低延迟的部署需求。
2.4 本章小结
本章系统综述了国内外高帧频红外弱小目标检测与识别的研究现状，涵盖了从传
统图像处理方法（如空域滤波、背景差分、时域积累等）到深度学习驱动方法（卷积
神经网络、Transformer、大模型等）的技术演进路径。国外研究起步较早，在多模态
传感器数据融合、复杂背景下的鲁棒检测以及与航空航天、卫星平台的工程化集成等
方面积累了较为成熟的技术体系。近年来，国外逐步引入注意力机制与时序建模方法，
以提升低信噪比和远距离探测条件下的检测稳定性。国内研究则更注重特定应用场景
的适配性与算法国产化，在端侧轻量化、实时性优化等方向取得了积极进展，并在基
于国产大模型的多任务特征共享与跨场景适应性方面展现出一定优势。
总体来看，当前研究仍存在以下不足：
22
● 低信噪比条件下鲁棒性有限，弱辐射小目标易受热噪声与传感器噪声影响，导
致检测率下降；
● 复杂背景抑制能力不足，在云层、海面、城市建筑等动态杂波干扰下，误检与
漏检问题突出；
● 轻量化与高精度难以兼顾，在端侧算力、存储与功耗受限条件下同时实现高精
度与实时性仍具挑战；
● 跨场景泛化能力不足，模型在不同平台、任务与气象条件下往往需要重新训练
或大规模微调；
● 时序特征利用不足，对高帧频序列所蕴含的运动信息挖掘不够深入。
针对上述问题，未来的发展方向包括：
● 融合多尺度空间特征与时序信息建模，提升低信噪比条件下的目标可检测性；
● 构建自适应背景抑制机制，增强复杂背景场景下的检测稳定性；
● 发展适用于端侧部署的轻量化大模型压缩与优化技术，实现精度与实时性的平
衡；
● 引入领域自适应与多任务学习框架，提升跨平台与跨气象条件的泛化能力；
● 加强国产大模型在垂直领域的训练与优化，确保技术的自主可控与安全可靠。
本章的分析表明，尽管国内外在该领域已取得显著进展，但关键技术环节仍有改
进空间。基于上述差距与发展趋势，下一章将提出一种结合国产轻量化大模型优势的
高帧频红外弱小目标检测与识别优化方案，以实现高精度、低延迟与强适应性并重的
技术目标。
23
3 算法设计方案
详细介绍算法设计方案，突出优化方向和创新点。
可以包括总体技术路线，数据预处理与增强，模型设计与建模，模型优化与适
应性提升等方面。
3.1 总体技术路线
在高帧频红外弱小目标检测中，红外弱小目标存在显著的类型差异，如小目标像
素占比极小、信噪比低，大目标轮廓清晰却需时序稳定性支撑，多目标存在遮挡重叠
等复杂交互等，传统单一检测方法难以兼顾不同目标的特征需求，易导致小目标被大
目标样本淹没、时序信息浪费等问题；此外，通用大模型虽具备强特征学习能力，但
与端侧平台的轻量化、低功耗要求存在天然冲突，而简单的多帧融合或时序建模方法
又存在计算开销大、抗噪性差等缺陷。针对上述核心难题，本方案以 “数据预处理赋
能轻量化国产大模型” 为核心技术路径，通过前端数据智能优化与后端模型高效适配
的协同设计，破解 “低信噪比、复杂背景、端侧资源约束” 三大核心矛盾。
基于此，本方案构建的整体技术框架（如图 3-1 所示）以轻量化场景分类器为决
策中枢，串联起场景自适应数据预处理与轻量化国产大模型两大协同模块，形成 “分
类引导处理、处理适配模型” 的闭环逻辑。两大模块的协同逻辑在于：通过前端预处
理主动提升数据质量（抑制背景、增强目标），从源头降低后端模型的学习难度，减
少对模型规模的依赖；后端轻量化大模型则专注于挖掘预处理后数据中的深层时空特
征，最终在国产化昇腾平台（Ascend 910B2）实现高效部署，达成 “高精度 - 低功耗 -
高实时性” 的统一。
图 3-1
24
（1）轻量化场景分类器
作为整个技术框架的 “智能诊断中心”，采用 MobileNetV2 作为主干网络，通过迁
移学习与精简分类头实现快速推理，精准识别输入红外序列的场景类型 ——“小目标”
（像素占比极小、信号微弱）、“大目标”（轮廓清晰、像素占比大）或 “复杂多目标”
（含多个不同类型目标，存在遮挡重叠）。分类结果直接决定后续预处理策略与模型
微调方向，是实现 “按需处理、精准适配” 的核心前提。
（2）场景自适应数据预处理
构建基于"先诊断后处理"模式的智能处理流水线，其技术核心在于采用
MobileNetV2 轻量化分类模型对红外序列图像的场景类型（包括小目标场景、大目标场
景及多目标场景）实施实时识别与分类，并依据分类结果动态调用相应的优化处理策
略：针对小目标场景，实施大核中值滤波与傅里叶-高斯高通滤波相结合的双域协同降
噪方案； 针对大目标及多目标场景， 运用基于 5 帧滑动窗口的时序建模方法进行运动
特征提取；在通用处理环节，集成 EnhanceNet 低光照图像增强算法与 Albumentations
数据增强技术，以全面提升系统的环境适应性与处理鲁棒性。
（3）轻量化国产大模型检测识别
为实现端侧适配，基于国产大模型 Qwen2. 5-VL 与 YOLOv7 构建混合检测架构，通
过多维度优化达成目标。在模型轻量化改造上，采用量化感知训练、结构化剪枝（通
道重要性评估）、知识蒸馏等技术，在保留核心特征提取能力的同时，有效降低模型
参数量与计算开销，以适配端侧硬件约束；在差异化微调策略上，依托 AdaLoRA 微调
25
方式，针对小目标、多目标、大目标的特征差异，分别结合跳帧（小目标/多目标）与
时间窗口（大目标）的数据处理方式进行微调，强化模型对不同场景的适配性；在时
序信息利用上，通过序列级微调和动态上下文建模，深度挖掘视频帧间的运动连续性
特征，显著提升目标检测的时空稳定性，减少虚警与漏检情况。
3.2 轻量化场景分类器
在现代红外图像分析中，分类器扮演着至关重要的角色，尤其是在面对复杂多变
的目标与背景时。为了提高分析精度并优化后续处理策略的选择，分类器将场景划分
为多个类别，这种方法不仅是为了提高图像处理的效率，更是为了确保在不同情境下
能够灵活选择最合适的增强技术。
分类的核心动机源于红外图像中的目标特性差异。每种目标类型都有其独特的视
觉特征和信号行为，例如，小目标往往信号微弱、对比度低，而大目标则表现出明显
的亮斑或区域，且目标与背景之间的对比度较高。对于复杂背景和多目标的情况，目
标间的干扰以及噪声问题可能导致传统处理方法的效果大打折扣。为了有效解决这些
问题，分类器需要根据不同场景的特点，对图像进行精准划分，并为后续增强策略提
供决策依据。
通过基于 MobileNetV2 的深度学习框架， 分类器采用了现代计算机视觉领域的领
先技术。MobileNetV2，作为一个轻量级的深度卷积神经网络，在移动端设备上已经证
明其强大的表现力与计算效率。 通过深度可分离卷积技术， MobileNetV2 能够在保持
高精度的同时，大幅减少参数量和计算复杂度，这使得该网络非常适合处理复杂图像
分类任务。分类器通过迁移学习加载在 ImageNet 上预训练的权重，并在此基础上微调
网络以适应特定的红外图像场景。根据任务需求，我们将原始的分类头替换为更精简
的结构：
这个新的分类头能够将主干网络提取的高维特征有效地映射到我们所需的三类场景：
以暗点为主的小目标（small_spot）、以亮斑为主的大目标（big_spot）和背景复杂、含
多目标（various_spot）。
分类过程本质上是一个多维度特征提取和判别的过程。通过对图像中目标的几何
特征、颜色对比度、纹理信息以及目标大小等多方面的特征进行学习，分类器能够自
动识别不同类别的图像特征。具体来说，对于每种类型的图像，分类器根据目标在图
26
27
3.3 场景自适应数据预处理
在红外弱小目标检测任务中，原始图像数据常存在信噪比低、目标与背景对比度
弱、目标形态多样（如单/多目标、尺寸不一）以及背景杂波复杂等固有挑战。若采用
单一、固定的预处理流程，难以自适应地应对多变的场景，甚至可能在抑制背景的同
时削弱目标信号。为解决这一难题，本章设计了一套自适应、多阶段的场景感知数据
像中的表现形式，如目标大小、亮度分布、背景复杂度等，进行分类。这不仅为后续
预处理与增强流水线（ Pipeline）。 依据分类结果， 流水线会调用针对性的背景抑制策
略，对不同场景采用最优的信号提取方法，旨在最大化地凸显目标、滤除无关干扰。
处理提供了更精确的输入，也确保了后续增强策略的选择能够更加针对性，避免了不
必要的计算浪费和处理过度。
技术上，分类器的设计是依托于先进的深度学习方法，结合了移动端优化的高效
架构与大规模数据训练的强大能力。利用深度神经网络在高维特征空间中的学习与分
类能力，我们能够从海量图像数据中自动提取有用信息，准确区分小目标、大目标和
多目标场景。这种基于智能分析的分类方法，结合了现有深度学习模型的技术优势，
同时为后续的图像增强和目标识别任务提供了更加稳定和可靠的决策支持。
图 3-3
通过一套包含主干增强、图像质量优化和通用随机变换的多层次数据增强方案，进一
步强化目标特征，并提升模型对各类真实世界干扰的鲁棒性。
图 3-2
（1）小目标（small_spot）
小目标图像中，目标往往较小且信号微弱，信噪比较高。由于目标与背景的差异
不明显，容易导致目标信息的丢失。对于这类图像，我们采用较为激进的处理策略，
如增强局部对比度、放大目标区域等方法，旨在提升目标特征的可见性，从而确保分
类器能够准确地识别出这些微弱的目标。
（2）大目标（big_spot）
大目标图像通常具有明显的亮斑或区域，目标与背景之间的对比度较高，且目标
的轮廓清晰。对于大目标图像，采用温和的处理策略，主要通过常规的图像增强（如
亮度调节、平滑等）来保证目标的完整性与稳定性，以避免过度增强导致的特征失真。
（3）多目标（various_spot）
多目标图像中，目标的尺寸不一，且目标之间存在较多干扰，背景复杂且噪声较
多。由于目标混杂，分类任务面临较高的误检与漏检风险。在这类图像中，我们采用
细致的增强策略，如局部对比度增强、目标区域的强化等，避免过度增强背景或无关
目标，减少误检和漏检，提高分类的准确性与鲁棒性。
28
3.3.1 小目标场景
小目标图像中，目标往往较小且信号微弱，信噪比较高。由于目标与背景的差异
不明显，容易导致目标信息的丢失。对于这类图像，我们采用较为激进的处理策略，
如增强局部对比度、放大目标区域等方法，旨在提升目标特征的可见性，从而确保分
类器能够准确地识别出这些微弱的目标。
（1）背景抑制
我们采用大核 57×57 的中值滤波器 M 对原始图像 I 进行处理。中值滤波的物理意
义在于，它能够有效地估计图像中像素值变化平缓的低频成分，这通常对应于红外图
像中的大面积背景区域。同时，中值滤波对椒盐噪声不敏感，相较于均值滤波，它能
更好地保护图像的边缘信息。
通过将原图 I 与估计出的背景图 B 相减，我们可以得到一个几乎只包含目标和高
频噪声的前景残差图 F。
（2）数据增强
在小目标背景被抑制后，残差图中仍可能包含部分背景残余和噪声。我们采用傅
里叶变换将图像 I(x, y)转换至频域 F(u, v)。
应用高斯高通滤波器 H( u, v)。高通滤波器的作用是衰减或移除图像中心的低频成
分，同时保留并增强与目标和边缘相关的高频细节。
通过傅里叶反变换，最终得到的图像 I’(x, y)对比度显著提升，微弱的目标变得更
加清晰可辨。
29
3.3.2 大目标场景
大目标图像通常具有明显的亮斑或区域，目标与背景之间的对比度较高，且目标
的轮廓清晰。对于大目标图像，采用温和的处理策略，主要通过常规的图像增强（如
亮度调节、平滑等）来保证目标的完整性与稳定性，以避免过度增强导致的特征失真。
（1）背景抑制
我们采用滑动窗口策略，对当前帧 It 及其前后若干连续帧（共 5 帧）进行均值或
中值运算，从而构建一个平滑且稳定的动态背景模型。这种方法能够有效滤除静态杂
波和传感器的随机噪声。
为了解决视频序列起始和末尾帧无法形成完整窗口的问题，我们引入了镜像填充
技术，确保每一帧都能得到有效处理。
（2）数据增强
大目标增强采用 Albumentations 增强，Albumentations 增强策略旨在通过一系列随
机变换，在不改变标签的前提下，模拟真实世界图像采集可能遇到的各种干扰和扰动。
光照与对比度模拟：以 80%的概率，随机应用 CLAHE（局部对比度自适应直方图
均衡）或随机亮度和对比度调整。这两种方法分别从局部和全局层面调整图像的对比
度和亮度，有效模拟不同光照条件（如日间、黄昏）以及传感器动态范围变化带来的
影响。
成像质量退化模拟：以 40%的概率，随机引入 ISONoise（模拟相机在高 ISO 设置
下的传感器噪声） 或运动模糊。 ISONoise 能够模拟低光照环境下常见的颗粒状噪声，
而运动模糊则模拟了目标或相机平台在采集瞬间发生位移所导致的图像模糊。这两种
增强方法使模型能够更好地应对真实成像系统中的不完美情况。
几何与尺寸归一：最后，所有图像都会经过几何变换，并统一缩放到模型所需的
固定输入尺寸。这是深度学习模型进行批量处理的关键步骤，确保输入维度的一致性，
为模型的稳定训练奠定基础。
30
3.3.3 多目标场景
多目标图像中，目标的尺寸不一，且目标之间存在较多干扰，背景复杂且噪声较
多。由于目标混杂，分类任务面临较高的误检与漏检风险。在这类图像中，我们采用
细致的增强策略，如局部对比度增强、目标区域的强化等，避免过度增强背景或无关
目标，减少误检和漏检，提高分类的准确性与鲁棒性。
（1）背景抑制
我们采用滑动窗口策略，对当前帧 It 及其前后若干连续帧（共 5 帧）进行均值或
中值运算，从而构建一个平滑且稳定的动态背景模型。这种方法能够有效滤除静态杂
波和传感器的随机噪声。
为了解决视频序列起始和末尾帧无法形成完整窗口的问题，我们引入了镜像填充
技术，确保每一帧都能得到有效处理。
（2）数据增强
通过计算连续帧 It(x, y)和 It+1(x, y)之间的像素级绝对差分，我们可以得到一幅运动
显著性图 M(x, y)，其中高亮的区域代表发生变化的动态区域，通常对应于运动目标。
将原始帧 It(x, y)、背景抑制后的前景信息 F(x, y)以及运动显著性图 M(x, y)进行加
权融合。 这种融合方式使得最终输出的图像 Ifinal( x, y) 既能突出运动目标的轮廓细节，
又能保留必要的场景结构信息，为后续的识别任务提供丰富的上下文。
3.4 轻量化国产大模型检测识别
红外弱小目标检测往往目标特征多样，如小目标像素占比极小、信噪比低，缺乏
稳定形状与纹理信息，易被背景噪声掩盖；大目标轮廓相对清晰，但其检测稳定性高
度依赖视频序列中的时序连续性信息；多目标场景因存在目标遮挡、重叠等复杂交互，
对模型的多目标分离与抗干扰能力提出更高要求。传统单一目标检测策略难以适配不
31
同目标的特征差异，导致小目标易被大目标样本淹没、大目标的时序优势无法发
挥；多数方法基于单帧检测范式，忽略帧间时序关联性，而少数尝试利用时序信息的
方法（如帧差法、3D CNN 等）要么计算开销大、对噪声敏感，要么难以满足高帧频
实时检测需求。同时，国产大模型（如 Qwen2.5-VL）虽具备序列建模与高效微调能力，
但在红外图像特征理解、与专用检测架构（如 YOLO）的结合及差异化时序处理等方
面仍存在技术空白。
为解决上述问题，本研究设计。。通过。。构建。。实现。。
基于此，本部分将聚焦模型的差异化设计与高效建模，重点阐述针对不同目标类
型的微调策略、时序信息的深度挖掘方法，以及基于轻量化国产大模型的混合检测架
构构建，通过精细化优化实现模型对红外弱小目标的精准检测与端侧高效适配。
3.4.1 时序数据处理
（1）
（2）
3.4.2 检测大模型微调架构设计
（1）预训练模型的领域适应挑战
Qwen2. 5-VL 作为通用视觉- 语言大模型， 在预训练阶段主要学习了自然图像和通
用视觉任务的知识表示。然而，红外弱小目标检测任务存在显著的领域特异性挑战：
设预训练数据分布为，目标任务数据分布为，则存在分布偏移：
其中 ， 为可接受阈值。
具体挑战分析：
红外图像与可见光图像的光谱特征差异
弱小目标与预训练中的常规目标差异
红外场景的热辐射干扰与自然场景差异
精确目标定位与通用视觉理解差异
32
（2）场景差异化微调的依据
基于多任务学习理论和元学习范式，不同场景的检测任务虽然共享基础视觉表示，
但在任务特定参数空间中存在显著差异。
通过对 26 个视频序列的统计分析，我们量化了三类场景的特征差异：
其中每个场景的特征分布可表示为：
KL 散度量化结果：
因此，微调需要分场景来设计，确保产生针对性更强的效果。
（3）AdaLoRA：自适应低秩微调技术
传统 LoRA 采用固定秩分解，无法适应不同场景的复杂度需求，我们提出自适应动
态 LoRA，包括动态秩分配机制，场景感知门控，梯度解耦优化。
图示
AdaLoRA 数学公式：
其中：
动态重要性权重：
场景感知门控：
自适应缩放因子：
动态秩分配算法：
其中复杂度系数：
：小目标需要精细但简单的特征
：多目标需要复杂的表示能力
：大目标使用标准复杂度
33
34
3.4.3 模型轻量化与泛化性增强
（1）模型轻量化优化
在高帧频弱小目标检测任务中，Qwen-2.5-VL 具备强大的多模态特征抽取与跨尺
度理解能力，但其数十亿级参数量与高计算复杂度使得在机载、弹载、星载等端侧平
台部署面临严重的实时性与能耗挑战。针对这一问题，本项目引入跨尺度特征蒸馏技
术，通过在训练阶段由全量 Qwen-2.5-VL 作为教师模型，将其在不同空间分辨率与
语义层级上的中间特征迁移到轻量化改造后的学生模型，从而在显著降低参数与运算
量的同时，保留对弱小目标的高精度感知能力。
具体的轻量化实现原理可参见图 。 首先利用结构化剪枝与低秩分解对 3-1
Qwen-2.5-VL 进行网络瘦身，减少通道数与层深，得到学生模型初始结构。在训练阶
段，将同一输入序列同时送入教师模型与学生模型，分别在模型的浅层（捕捉局部细
节）、中层（提取目标特征）与深层（整合全局信息）提取多尺度特征映射。随后，
通过尺度归一化与通道映射模块将教师特征调整到学生特征空间，使其可直接进行逐
尺度对齐，并计算特征蒸馏损失（L2 距离与注意力图 KL 散度结合）。该损失与学生
模型的检测任务损失加权联合优化，促使学生模型在压缩条件下仍能学习到教师模型
在弱小目标定位、背景抑制等方面的高效特征表达。
35
（2）模型泛化性增强
在弱小目标识别任务中， 具备高效的空间目标定位能力，而 YOLOv7
Qwen-2.5-VL 拥有强大的跨模态语义理解与时空信息整合能力。二者在特征建模上的
互补性为提升泛化性提供了契机。然而，在复杂场景中，这两类模型的特征分布可能
存在较大差异，如果分别独立训练，容易出现某一模型在特定背景下性能下降的问题。
为此，本项目引入双模型特征一致性正则化技术，在训练阶段通过共享增强样本和特
征对齐约束，使两种架构在中间层的语义空间中保持一致性，从而显著提升整体系统
在跨场景、跨背景条件下的鲁棒性。
图 3-1：模型轻量化实现原理
具体的泛化性增强原理可参见图 3-2。首先被预处理算法增强后的数据并行输入
YOLOv7 与 Qwen-2.5-VL，分别提取其在关键中间层（YOLOv7 的 Backbone 中 C3
模块输出特征，Qwen-2.5-VL 的视觉编码器中高层特征图）上的多尺度特征表示。接
着，通过对齐模块对两个特征映射进行维度变换与尺度归一化，并计算一致性损失
（余弦相似度损失和特征分布的 KL 散度），该损失与各自的检测损失共同反向传播，
迫使两类模型在不同输入条件下提取到更具泛化性的目标表征。同时，在最终的推理
阶段，即使某一模型在特定域中出现性能波动，另一模型的特征一致性学习所带来的
表征稳定性也能有效补偿，从而也保证了整体检测系统的稳健性。
图 3-2：模型泛化性增强原理
3.4.4 小目标与多目标：基于跳帧的大模型微调
小目标检测面临一个根本矛盾：需要足够的时序信息来区分真实目标和噪声，但
又不能承受逐帧处理的巨大计算开销。我们发现，小目标的运动模式相对简单且可预
测，这为跳帧处理提供了理论基础。
通过分析 data01、data02 等小目标序列，我们观察到目标在 5 帧间隔内的位置变
化基本呈线性，这意味着可以通过跳帧采样获取关键时序信息，然后用插值方法恢复
中间帧。
通过构建跳帧序列作为训练输入，让模型学习帧间的长距离时序依赖关系。
36
37
3.4.5
'sequence_length': 5
}
我们在 AdaLoRA 微调时专门训练模型处理跳跃序列。模型需要学会：
从跳跃的帧中推断目标的运动轨迹,预测缺失帧中目标的可能位置,判断检测结果的
时序合理性。
损失函数也相应调整，不仅要求检测准确，还要求预测的中间帧位置合理。
大目标微调：基于时间窗口的大模型微调
图示
时间窗口策略通过滑动窗口微调，让模型学习连续时间段内的目标演化模式，特别
适合大目标的时序建模需求。
跳帧序列构建：
时序编码器微调：
其中 为跳帧专用的 LoRA 参数。
跨帧注意力微调：
其中通过跳帧 LoRA 微调得到： ,
跳帧微调损失函数：
其中：
运动一致性损失：
时序连贯性损失：
插值损失：
跳帧 LoRA 配置：
skip_frame_lora_config = {
'r': 12,
'lora_alpha': 24,
'lora_dropout': 0.04,
'target_modules': [
"q_proj", "k_proj", "v_proj",
"temporal_proj",
"interpolation_head"
],
'skip_interval': 5,
38
'r': 16,
'lora_alpha': 32,
'lora_dropout': 0.05,
'target_modules': [
"q_proj", "k_proj", "v_proj", "o_proj",
"context_proj",
"window_fusion",
"prompt_generator"
],
'window_size': 7,
'stride': 3,
'overlap_weight': 0.3
}
图示
我们提取每帧的时序统计信息，然后通过 AdaLoRA 微调让模型理解这些信息的含
义,增强了文本提示的生成和理解能力。
滑动窗口构建：
其中 为窗口大小，为滑动步长。
时序上下文编码：
文本化融入微调：
多窗口注意力微调：
其中权重矩阵通过窗口 LoRA 微调：
时间窗口微调损失函数：
其中：
上下文损失：
一致性损失：
重叠损失：
时间窗口 LoRA 配置：
window_lora_config = {
每个训练样本不仅包含图像，还包含丰富的时序上下文描述。模型需要学会将这
些文本信息与视觉特征结合，做出更准确的检测判断。
这两种策略的微调架构设计充分体现了针对不同目标类型的差异化优化思路，通
过精密的 LoRA 配置和损失函数设计，实现了高效的参数微调和性能提升。
3.4.6
3.4.7 检测与指标计算策略
线性插值检测算法：针对小目标和多目标的跳帧检测结果，我们设计了线性插值算
法来恢复缺失帧的目标位置信息。该算法的核心是基于目标匹配的线性插值方法。
首先，算法需要在相邻的检测帧之间建立目标对应关系。这通过计算目标间的相似
度来实现，包括位置距离、尺寸差异。建立对应关系后，算法对每对匹配的目标进行
线性插值，计算中间帧的目标位置。
插值过程不仅考虑位置信息，还考虑目标的尺寸、置信度等属性。对于置信度的插
值，我们采用保守策略，取两个端点置信度的最小值，以确保插值结果的可靠性。
时间窗口融合检测：对于大目标的时间窗口检测结果，需要进行有效的融合以生成
最终的检测结果。由于采用了重叠窗口策略，同一帧可能出现在多个窗口中，因此需
要设计合理的融合算法。
融合算法采用加权平均的策略，权重的计算考虑了窗口的重叠程度、检测置信度、
时序一致性等因素。对于重叠区域的检测结果，算法会综合多个窗口的输出，生成更
加稳定和准确的最终结果。
为了全面评估不同检测策略的性能，我们建立了多维度的性能评估体系。该体系
包括检测精度指标、时序稳定性指标、计算效率指标等多个维度。
模型优化与适应性提升
3.4.1 模型轻量化与优化
为在弱小目标检测任务中兼顾精度与效率，本研究针对 Qwen-2. 5-VL 与 YOLOv7
的不同结构特点，设计了面向计算与存储优化的轻量化策略。两种方案均围绕减少冗
余计算与压缩模型参数展开，分别从数值精度压缩、网络结构裁剪及卷积计算优化等
39
方面入手，在保持核心特征提取能力的同时显著降低推理延迟与资源占用，从而提升
模型在实时性与硬件受限场景下的适配性。
（1）Qwen-2.5-VL 模型的轻量化与优化
针对 Qwen-2. 5-VL 模型计算量大、 存储需求高的特点， 采用量化感知训练
（Quantization-Aware Training, QAT）将模型中 FP16 格式的权重与激活值压缩至 INT8
表示。在训练阶段，通过在前向传播中引入伪量化算子模拟量化误差，反向传播与参
数更新仍保持浮点精度，使模型在训练过程中逐步适应低精度表示，从而减少量化带
来的性能损失。该方法在不改变模型结构的前提下显著降低参数存储需求，并减少推
理过程中的运算开销，适用于实时性要求较高及硬件资源受限的部署环境。
（2）YOLOv7 模型的轻量化与优化
针对 YOLOv7 模型的高计算量问题， 从网络结构压缩与卷积计算优化两个方面进
行改进。在结构剪枝方面，通过计算各通道的重要性评分，识别对小目标检测贡献有
限的特征通道，并采用基于通道重要性排序的结构化剪枝策略，移除重要性较低的通
道，从而减少模型参数量与计算量。在卷积优化方面，将部分标准卷积替换为深度可
分离卷积（Depthwise Separable Convolution），将卷积分解为深度卷积与逐点卷积两个
步骤，以降低乘加运算次数和参数规模。这一组合方案在压缩模型规模的同时，保留
了关键特征提取能力，并增强了模型在多种硬件平台上的部署适应性。
3.4.2 训练策略优化
针对弱小目标检测任务中目标信号弱、背景干扰强、样本分布复杂等挑战，本研
究构建了由数据增强与动态学习率调整组成的多层次训练优化方案，旨在提升模型的
特征捕捉能力与泛化性能。
（1）数据增强策略
在输入数据层面，通过多样化处理扩大训练样本分布范围，以增强模型对复杂场
景的适应性。 首先， 利用 CLAHE 直方图均衡化与随机亮度、 对比度变化模拟多种光
照条件，减轻环境光变化对检测精度的影响。其次，针对红外弱小目标信号特征微弱
的问题，引入高斯噪声注入与运动模糊模拟，提升模型在低信噪比条件下的特征提取
能力。上述多维度增强手段协同作用，不仅丰富了样本的光谱与空间特性，也有效提
高了模型在真实复杂场景下的鲁棒性。
40
（2）动态学习率调整
在优化算法层面，采用结合梯度累积的自适应学习率调度，以适应大规模参数更
新需求并防止模型陷入局部最优。具体策略包括：初始阶段设置较低学习率以确保训
练稳定性；在预热阶段结束后提高学习率，加速参数收敛；在后期逐步降低学习率以
实现精细化优化。该策略针对 Qwen-2.5-VL 与 YOLOv7 的微调过程分别进行参数化设
计， 并结合 LoRA 微调框架对梯度累积与部分参数冻结进行优化， 从而在有限计算资
源下保持较高的训练效率与泛化能力。
通过将数据增强与动态学习率调整相结合，本研究在样本多样性与优化自适应性
两方面形成互补，构建了一套面向弱小目标检测任务的高效训练策略，有助于在不同
硬件平台与应用场景中实现性能与效率的平衡。
41
4 算法部署方案
详细介绍算法部署相关方案。
可以包括目标部署平台与方案介绍，模型转化与优化，部署环境配置，部署验
证与性能等方面。
4.1 目标部署平台与方案
Arch：aarch64
CPU：Huawei Kunpeng 920
NPU：Huawei Ascend 910B2（64GB）
Ascend HDK：24.1.RC3
CANN：8.1.RC1
在完成算法设计后，我们基于 vllm-ascend 框架在国产 NPU Huawei Ascend 910B2
上运行并验证我们的方案，从而形成一套完整的大模型面向国产化 AI 芯片嵌入式平
台的部署方案，有力地证实了本方案的实际运行效果。
4.2 模型转化与优化
针对国产化 AI 芯片嵌入式平台的资源约束，本研究构建了模型量化技术框架，在
保持检测精度的前提下显著提高模型运行速度。
量化技术通过降低数值精度实现模型压缩。本研究针对 Transformer 架构特点，设
计了混合精度量化方案，对注意力机制保持较高精度以确保计算稳定性，对前馈网络
采用 INT8 量化并通过校准数据集优化量化参数。量化感知训练在前向传播中引入伪量
化算子模拟量化误差，使模型逐步适应低精度表示，有效减少量化带来的性能损失。
通过校准策略和误差补偿机制，实现了在保持检测精度的同时大幅降低模型存储需求。
4.3 部署环境配置
vllm-ascend 的版本受到 CANN 版本的限制（见
https://vllm-ascend.readthedocs.io/en/v0.7.3-dev/installation.html）。在 8.1.RC1 的
CANN 下，vllm-ascend 的最高稳定版本为 v0.7.3-dev。下面的部署代码均基于
v0.7.3-dev 的 vllm-ascend。
42
conda create --name test310 python==3.10 -y
conda activate test310
apt update -y
apt install -y gcc g++ libnuma-dev git
pip install vllm==0.7.3
pip install vllm-ascend==0.7.3.post1 --extra-index https://download.pytorch.org/whl/cpu/
pip install torch-npu:2.5.1 modelscope
pip install torchvision==0.20.1 qwen_vl_utils --extra-index-url
https://download.pytorch.org/whl/cpu/
4.4 部署验证与性能
针对在国产化 AI 芯片嵌入式平台上部署大模型进行目标检测识别的应用场景，我
们在保障工程可实现的基础上，通过模型量化提高推理的吞吐量，进而提高对高帧频
图像序列的处理速度。
cd deploy/
export VLLM_USE_MODELSCOPE=True
export PYTORCH_NPU_ALLOC_CONF=max_split_size_mb:256
python vl_yy.py
在不进行量化并使用单张 NPU 推理时，平均每帧耗时 0.1s。
为了提高运行速度，我们使用 MindStudio ModelSlim（msModelSlim，昇腾模型压
43
缩工具）进行 W8A8 量化。
git clone https://gitee.com/ascend/msit -b modelslim-VLLM-8.1.RC1.b020_001
cd msit/msmodelslim
bash install.sh
pip install accelerate
cd example/Qwen2.5-VL
export ASCEND_RT_VISIBLE_DEVICES=0
export PYTORCH_NPU_ALLOC_CONF=expandable_segments:False
python quant_qwen2_5vl.py \
--model_path /root/autodl-tmp/Qwen2.5-VL-7B-Instruct-merged \
--calib_images /root/autodl-tmp/tiaozhanbei_datasets_1/images/data13/ \
--save_directory /root/autodl-tmp/Qwen2.5-VL-7B-Instruct-merged-w8a8 \
--w_bit 8 --a_bit 8 --device_type npu --trust_remote_code True --anti_method m4
在使用单张 NPU 推理时，平均每帧耗时 0.08s。
44
5 验证与实验
体现算法性能验证结果。
可以包括实验设置，实验结果与分析等方面。
5.1 实验设置
5.2 实验结果与分析
45
6 总结与展望
对本次工作进行总结。
可以总结本次算法设计与算法部署工作中遇到的问题和难点，并形成相关结
论。
可以对大模型，算法端侧部署等方面在国防武器装备方面有待突破的问题及未
来也无法突破的一些瓶颈做论述。
注：以上模板中各章节内容仅为建议，供各参赛队伍参考，撰写报告时可以包
括但不限于以上内容。
6.1 工作总结
6.2 未来展望
46
参考文献
[1] Frantar E, Ashkboos S, Hoefler T, et al. Gptq: Accurate post-training quantization for
generative pre-trained transformers[J]. arXiv preprint arXiv:2210.17323, 2022.
[2] Jacob B, Kligys S, Chen B, et al. Quantization and training of neural networks for
efficient integer-arithmetic-only inference[C]//Proceedings of the IEEE conference on
computer vision and pattern recognition. 2018: 2704-2713.
[3] Hu H, Peng R, Tai Y W, et al. Network trimming: A data-driven neuron pruning approach
towards efficient deep architectures[J]. arXiv preprint arXiv:1607.03250, 2016.
[4] Molchanov P, Tyree S, Karras T, et al. Pruning convolutional neural networks for resource
efficient inference[J]. arXiv preprint arXiv:1611.06440, 2016.
[5] He Y, Zhang X, Sun J. Channel pruning for accelerating very deep neural
networks[C]//Proceedings of the IEEE international conference on computer vision. 2017:
1389-1397.
[6] Guo Y, Yao A, Chen Y. Dynamic network surgery for efficient dnns[J]. Advances in
neural information processing systems, 2016, 29.
[7] Liu Z, Xu J, Peng X, et al. Frequency-domain dynamic pruning for convolutional neural
networks[J]. Advances in neural information processing systems, 2018, 31.
[8] Liu N, Ma X, Xu Z, et al. Autocompress: An automatic dnn structured pruning framework
for ultra-high compression rates[C]//Proceedings of the AAAI conference on artificial
intelligence. 2020, 34(04): 4876-4883.
[9] Hinton G, Vinyals O, Dean J. Distilling the knowledge in a neural network[J]. arXiv
preprint arXiv:1503.02531, 2015.
[10] Ba J, Caruana R. Do deep nets really need to be deep?[J]. Advances in neural
information processing systems, 2014, 27.
[11] Romero A, Ballas N, Kahou S E, et al. Fitnets: Hints for thin deep nets. arXiv 2014[J].
arXiv preprint arXiv:1412.6550, 2014.
[12] Zagoruyko S, Komodakis N. Diracnets: Training very deep neural networks without
skip-connections[J]. arXiv preprint arXiv:1706.00388, 2017.
47
48
[21] 蒋昕昊, 蔡伟, 杨志勇, 等. 基于 YOLO-IDSTD 算法的红外弱小目标检测[J]. 红外与
激光工程, 2022, 51(3): 20210106.
[22] 蔺向明. 基于红外成像的小目标检测技术研究[J]. 航空兵器, 2014(3): 12–15.
[23] Wang X, Li Y, Zhang H, et al. Infrared small target detection via multidirectional
derivative joint contrast measure[J]. Optics and Laser Technology, 2025, 178: 111234.
[24] Cheng X, Liu Y, Zhang Z, et al. IRWT-YOLO: A Background Subtraction-Based
Method for Anti-Drone Detection[J]. Drones, 2025, 9(4): 297.
DOI:10.3390/drones9040297.
[25] Qiu Z, Ma Y, et al. Improved DBSCAN for infrared cluster small target detection[J].
[13] Park W, Kim D, Lu Y, et al. Relational knowledge distillation[C]//Proceedings of the
IEEE Geoscience and Remote Sensing Letters, 2023, 20: 1–5.
IEEE/CVF conference on computer vision and pattern recognition. 2019: 3967-3976.
[14] Tian Y, Krishnan D, Isola P. Contrastive representation distillation[J]. arXiv preprint
arXiv:1910.10699, 2019.
[15] Sainath T N, Kingsbury B, Sindhwani V, et al. Low-rank matrix factorization for deep
neural network training with high-dimensional output targets[C]//2013 IEEE
international conference on acoustics, speech and signal processing. IEEE, 2013:
6655-6659.
[16] Jaderberg M, Vedaldi A, Zisserman A. Speeding up convolutional neural networks with
low rank expansions[J]. arXiv preprint arXiv:1405.3866, 2014.
[17] Yao Z, Wu X, Li C, et al. Exploring post-training quantization in llms from
comprehensive study to low rank compensation[C]//Proceedings of the AAAI
Conference on Artificial Intelligence. 2024, 38(17): 19377-19385.
[18] 刘杨帆, 曹立华,李宁,等. 基于 YOLOv4 的空间红外弱目标检测[J]. 液晶与显示, 2021,
36(4): 543–550.
[19] Liu Y, Cao L, Li N, et al. Pixel-wise attention driven infrared small target detection
network[J]. Journal of Northwestern Polytechnical University, 2024, 42(2): 335–343.
[20] 张骢, 韩自强, 岳明凯, 权康男. 反“低慢小”无人机红外检测方法研究[J]. 兵器装备
工程学报, 2023-07-25.
[26] 陈晓龙. 复杂场景红外弱小目标检测算法研究[D]. 中国科学院大学(中国科学院长春
光学精密机械与物理研究所), 2025.
[27] 孙扬. 复杂背景下的红外弱小目标检测技术研究[D]. 国防科技大学, 2020.
[28] 李拓. 复杂背景下的红外弱小目标检测研究[D]. 西安电子科技大学, 2020.
[29] 张群. 复杂背景下红外弱小目标检测方法研究[D]. 北方民族大学, 2025.
[30] 樊华. 红外弱小目标检测跟踪技术研究[D]. 北华航天工业学院, 2021.
[31] 崔书玮, 武文波. 基于时空域特征融合的红外弱小目标检测研究[J]. 航天返回与遥感,
2024, 45(5): 79-88.
[32] Marvasti F S, Mosavi M R, Nasiri M. Flying small target detection in IR images based
on adaptive toggle operator[J]. IET Computer Vision, 2018, 12(4): 527-534.
[33] Wang C, Wang L. Multidirectional ring top-hat transformation for infrared small
target detection[J]. IEEE Journal of Selected Topics in Applied Earth Observations and
Remote Sensing, 2021, 14: 8077-8088.
[34] Anju T S, Raj N R N. Shearlet transform based image denoising using histogram
thresholding[C]//2016 International Conference on Communication Systems and
Networks (ComNet). IEEE, 2016: 162-166.
[35] Wang X, Peng Z, Zhang P, et al. Infrared small target detection via
nonnegativity-constrained variational mode decomposition[J]. IEEE Geoscience and
Remote Sensing Letters, 2017, 14(10): 1700-1704.
[36] Chen C LP, Li H, Wei Y, et al. A Local Contrast Method for Small Infrared Target
Detection[J]. IEEE Transactions on Geoscience and Remote Sensing, 2014, 52(1):
574-581.
[37] Xia C, Li X, Zhao L, et al. Modified Graph Laplacian Model With Local Contrast and
Consistency Constraint for Small Target Detection[J]. IEEE Journal of Selected
Topics in Applied Earth Observations and Remote Sensing, 2020, 13: 5807-5822.
[38] Han J, Ma Y, Zhou B, et al. A Robust Infrared Small Target Detection Algorithm Based
on Human Visual System[J]. IEEE Geoscience and Remote Sensing Letters, 2014,
11(12): 2168-2172.
[39] Wei Y, You X, Li H. Multiscale patch-based contrast measure for small infrared target
detection[J]. Pattern Recognition, 2016, 58: 216-226.
49
[40] Bai X & Bi Y. Derivative Entropy-Based Contrast Measure for Infrared Small- Target
Detection[J]. IEEE Transactions on Geoscience and Remote Sensing, 2018, 56(4):
2452-2466.
[41] Han J, Liu C, Liu Y, et al. Infrared Small Target Detection Utilizing the Enhanced
Closest-Mean Background Estimation[J]. IEEE Journal of Selected Topics in Applied
Earth Observations and Remote Sensing, 2021, 14: 645-662.
[42] Lu R, Yang X, Li W, et al. Robust Infrared Small Target Detection via
Multidirectional Derivative-Based Weighted Contrast Measure[J]. IEEE Geoscience and
Remote Sensing Letters, 2022, 19: 1-5.
[43] 史雨欣.基于深度学习的复杂背景下红外弱小目标检测算法研究[D].北京交通大
学,2023.
[44] Zhao J, Tang Z, Yang J, et al. Infrared small target detection using sparse representation
[J]. Journal of Systems Engineering and Electronics, 2011, 22(6): 897–904.
[45] gang Zhao A, li Wang H, gang Yang X, et al. Small infrared target detection based on
improved structure collaborative sparseness[C]//2015 Chinese Automation Congress
(CAC). 2015: 752–756.
[46] Liu P, Peng J, Wang H, et al. Infrared small target detection via joint low rankness and
local smoothness prior[J]. IEEE Transactions on Geoscience and Remote Sensing, 2024,
62: 1–15.
[47] Yin J J, Li H C, Zheng Y B, et al. Spatial-temporal weighted and regularized tensor
model for infrared dim and small target detection[J]. IEEE Transactions on Geoscience
and Remote Sensing, 2024, 62: 1–17.
[48] SunY, LinZ, LiuT, et al. Adaptive spatial-temporal tensor and weighted tensor average
rank approximation for infrared small target detection[J]. IEEE Journal of Selected
Topics in Applied Earth Observations and Remote Sensing, 2025: 1–19.
[49] Wei H, Tan Y, Lin J. Robust infrared small target detection via temporal low-rank
andsparse representation[C]//2016 3rd International Conference on Information Science
and Control Engineering (ICISCE). 2016: 583–587.
50
[50] Deng X, Li W, Li L, et al. Low-rank and sparse decomposition on contrast map for
small infrared target detection[C]//2018 24th International Conference on Pattern
Recognition (ICPR). 2018: 2682–2687.
[51] Huang Z, Zhao E, Zheng W, et al. Infrared small target detection via two-stage feature
complementary improved tensor low-rank sparse decomposition[J]. IEEE Journal of
Selected Topics in Applied Earth Observations and Remote Sensing, 2024, 17:
17690–17709.
[52] Zhao B, Wang C, Fu Q, et al. A novel pattern for infrared small target detection with
generative adversarial network[J]. IEEE Transactions on Geoscience and Remote Sens ing, 2021, 59(5): 5881–5892.
[53] Ma Z, Pang S, Hao F. Generative adversarial differential analysis for infrared small
target detection[J]. IEEE Journal of Selected Topics in Applied Earth Observations and
Remote Sensing, 2024, 17: 6616–6626.
[54] Ding H, Huang N, Wu Y, et al. Infrared small target detection improvement via hybrid
data augmentation using diffusion models and gan[J]. IEEE Transactions on Aerospace
and Electronic Systems, 2024: 1–16.
[55] Qi M, Liu L, Zhuang S, et al. Ftc-net: Fusion of transformer and cnn features for
infrared small target detection[J]. IEEE Journal of Selected Topics in Applied Earth
Observations and Remote Sensing, 2022, 15: 8613–8623.
[56] Shi S, Song Y. An improved yolov3 infrared small target detection algorithm[M]//2024
5th International Conference on Computer Vision, Image and Deep Learning (CVIDL).
2024: 1356–1359.
[57] Liu B, Jiang Q, Wang P, et al. Irmsd-yolo: Multiscale dilated network with inverted
residuals for infrared small target detection[J]. IEEE Sensors Journal, 2025: 1–1.
[58] Gupta M, Chan J, Krouss M, et al. Infrared small target detection enhancement using a
lightweight convolutional neural network[J]. IEEE Geoscience and Remote Sensing
Letters, 2022, 19: 1–5.
[59] Ma T, Yang Z, Liu B, et al. A lightweight infrared small target detection network based
on target multiscale context[J]. IEEE Geoscience and Remote Sensing Letters, 2023, 20:
1–5.
51
[60] Wu Y, Ding H, Liu Y, et al. Synthetic data augmentation for infrared small target detec tion via exploring frequency components and targets prior[C]//2024 IEEE International
Conference on Multimedia and Expo (ICME). 2024: 1–6.
52
附件 1：团队介绍
介绍参赛队伍及合作队伍的各队员情况，若为研究生或博士，请补充介绍教研
室基本信息、研究方向、课题牵头人等相关情况。
53
